<?php
$requirements = checkSystemRequirements();
?>

<h2 style="color: #10b981; margin-bottom: 1rem;">System Requirements Check</h2>

<div class="requirements">
    <div class="req-item">
        <span>PHP Version (7.4+)</span>
        <span class="req-status <?= $requirements['requirements']['php_version'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['php_version'] ? '✓ ' . PHP_VERSION : '✗ ' . PHP_VERSION ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>PDO MySQL Extension</span>
        <span class="req-status <?= $requirements['requirements']['pdo_mysql'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['pdo_mysql'] ? '✓ Available' : '✗ Missing' ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>Mbstring Extension</span>
        <span class="req-status <?= $requirements['requirements']['mbstring'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['mbstring'] ? '✓ Available' : '✗ Missing' ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>OpenSSL Extension</span>
        <span class="req-status <?= $requirements['requirements']['openssl'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['openssl'] ? '✓ Available' : '✗ Missing' ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>cURL Extension</span>
        <span class="req-status <?= $requirements['requirements']['curl'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['curl'] ? '✓ Available' : '✗ Missing' ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>GD Extension</span>
        <span class="req-status <?= $requirements['requirements']['gd'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['gd'] ? '✓ Available' : '✗ Missing' ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>Config Directory Writable</span>
        <span class="req-status <?= $requirements['requirements']['writable_config'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['writable_config'] ? '✓ Writable' : '✗ Not Writable' ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>Assets Directory Writable</span>
        <span class="req-status <?= $requirements['requirements']['writable_assets'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['writable_assets'] ? '✓ Writable' : '✗ Not Writable' ?>
        </span>
    </div>
    
    <div class="req-item">
        <span>Logs Directory</span>
        <span class="req-status <?= $requirements['requirements']['writable_logs'] ? 'req-pass' : 'req-fail' ?>">
            <?= $requirements['requirements']['writable_logs'] ? '✓ Can Create' : '✗ Cannot Create' ?>
        </span>
    </div>
</div>

<?php if ($requirements['all_passed']): ?>
    <div style="margin-top: 2rem;">
        <button type="button" class="btn" onclick="location.href='?step=2'">
            Continue to Database Configuration
        </button>
    </div>
<?php else: ?>
    <div style="margin-top: 2rem;">
        <p style="color: #ef4444; text-align: center;">
            Please fix the above requirements before continuing.
        </p>
        <button type="button" class="btn" onclick="location.reload()" style="margin-top: 1rem;">
            Recheck Requirements
        </button>
    </div>
<?php endif; ?>
