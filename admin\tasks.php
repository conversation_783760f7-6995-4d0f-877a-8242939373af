<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

requireAdmin();

$page_title = 'Task Management';

// Handle actions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create_task') {
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category = $_POST['category'] ?? '';
            $reward_amount = floatval($_POST['reward_amount'] ?? 0);
            $sort_order = intval($_POST['sort_order'] ?? 1);
            
            if (empty($title) || empty($description) || empty($category) || $reward_amount <= 0) {
                $error_message = 'All fields are required and reward must be greater than 0';
            } else {
                $db = getDB();
                $stmt = $db->prepare("
                    INSERT INTO tasks (title, description, category, reward_amount, sort_order, is_active) 
                    VALUES (?, ?, ?, ?, ?, 1)
                ");
                if ($stmt->execute([$title, $description, $category, $reward_amount, $sort_order])) {
                    $success_message = 'Task created successfully';
                } else {
                    $error_message = 'Failed to create task';
                }
            }
        } elseif ($action === 'toggle_task') {
            $task_id = intval($_POST['task_id'] ?? 0);
            if ($task_id > 0) {
                $db = getDB();
                $stmt = $db->prepare("UPDATE tasks SET is_active = NOT is_active WHERE id = ?");
                if ($stmt->execute([$task_id])) {
                    $success_message = 'Task status updated successfully';
                } else {
                    $error_message = 'Failed to update task status';
                }
            }
        } elseif ($action === 'delete_task') {
            $task_id = intval($_POST['task_id'] ?? 0);
            if ($task_id > 0) {
                $db = getDB();
                $stmt = $db->prepare("DELETE FROM tasks WHERE id = ?");
                if ($stmt->execute([$task_id])) {
                    $success_message = 'Task deleted successfully';
                } else {
                    $error_message = 'Failed to delete task';
                }
            }
        }
    }
}

// Get all tasks
$db = getDB();
$stmt = $db->prepare("SELECT * FROM tasks ORDER BY category, sort_order, id");
$stmt->execute();
$all_tasks = $stmt->fetchAll();

// Group tasks by category
$tasks_by_category = [];
foreach ($all_tasks as $task) {
    $tasks_by_category[$task['category']][] = $task;
}

// Get task statistics
$stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_tasks,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_tasks,
        AVG(reward_amount) as avg_reward
    FROM tasks
");
$stmt->execute();
$task_stats = $stmt->fetch();

// Get completion statistics
$stmt = $db->prepare("
    SELECT 
        t.category,
        COUNT(DISTINCT ut.user_id) as unique_completions,
        COUNT(ut.id) as total_completions,
        SUM(CASE WHEN ut.reward_claimed = 1 THEN t.reward_amount ELSE 0 END) as total_rewards_paid
    FROM tasks t
    LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.is_completed = 1
    GROUP BY t.category
");
$stmt->execute();
$completion_stats = $stmt->fetchAll();

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-green-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">
                <i class="fas fa-tasks text-green-600 mr-3"></i>
                Task Management
            </h1>
            <p class="text-xl text-gray-600">Create and manage mining tasks for users</p>
        </div>

        <!-- Flash Messages -->
        <?php if ($success_message): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Tasks</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $task_stats['total_tasks'] ?? 0; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-list text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Tasks</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $task_stats['active_tasks'] ?? 0; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-check text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Avg Reward</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo formatCurrency($task_stats['avg_reward'] ?? 0); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-coins text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Categories</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo count($tasks_by_category); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-tags text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Create New Task -->
            <div class="lg:col-span-1">
                <div class="mining-card">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Create New Task</h2>
                        <p class="text-gray-600">Add a new task for users to complete</p>
                    </div>

                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="create_task">
                        
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Task Title</label>
                            <input type="text" id="title" name="title" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500">
                        </div>
                        
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea id="description" name="description" rows="3" required
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500"></textarea>
                        </div>
                        
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                            <select id="category" name="category" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500">
                                <option value="">Select Category</option>
                                <option value="daily">Daily</option>
                                <option value="social">Social</option>
                                <option value="mining">Mining</option>
                                <option value="referral">Referral</option>
                                <option value="achievement">Achievement</option>
                            </select>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="reward_amount" class="block text-sm font-medium text-gray-700 mb-2">Reward ($)</label>
                                <input type="number" id="reward_amount" name="reward_amount" step="0.01" min="0.01" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500">
                            </div>
                            
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                                <input type="number" id="sort_order" name="sort_order" min="1" value="1" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-green-500 focus:border-green-500">
                            </div>
                        </div>
                        
                        <button type="submit" class="w-full btn-green text-white py-3 rounded-lg font-semibold">
                            <i class="fas fa-plus mr-2"></i>Create Task
                        </button>
                    </form>
                </div>
            </div>

            <!-- Task List -->
            <div class="lg:col-span-2">
                <div class="mining-card">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">All Tasks</h2>
                        <p class="text-gray-600">Manage existing tasks and their settings</p>
                    </div>

                    <?php foreach ($tasks_by_category as $category => $tasks): ?>
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 capitalize">
                            <?php echo htmlspecialchars($category); ?> Tasks
                        </h3>
                        
                        <div class="space-y-4">
                            <?php foreach ($tasks as $task): ?>
                            <div class="border border-gray-200 rounded-lg p-4 <?php echo $task['is_active'] ? 'bg-white' : 'bg-gray-50'; ?>">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900 <?php echo !$task['is_active'] ? 'line-through text-gray-500' : ''; ?>">
                                            <?php echo htmlspecialchars($task['title']); ?>
                                        </h4>
                                        <p class="text-sm text-gray-600 mt-1"><?php echo htmlspecialchars($task['description']); ?></p>
                                        <div class="flex items-center space-x-4 mt-2">
                                            <span class="text-sm font-medium text-green-600">
                                                <i class="fas fa-coins mr-1"></i><?php echo formatCurrency($task['reward_amount']); ?>
                                            </span>
                                            <span class="text-sm text-gray-500">Order: <?php echo $task['sort_order']; ?></span>
                                            <span class="px-2 py-1 text-xs rounded-full <?php echo $task['is_active'] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                <?php echo $task['is_active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex space-x-2 ml-4">
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="toggle_task">
                                            <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                            <button type="submit" class="text-sm px-3 py-1 rounded <?php echo $task['is_active'] ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' : 'bg-green-100 text-green-800 hover:bg-green-200'; ?>">
                                                <?php echo $task['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                            </button>
                                        </form>
                                        
                                        <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this task?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="delete_task">
                                            <input type="hidden" name="task_id" value="<?php echo $task['id']; ?>">
                                            <button type="submit" class="text-sm px-3 py-1 rounded bg-red-100 text-red-800 hover:bg-red-200">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <?php if (empty($tasks_by_category)): ?>
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-tasks text-gray-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Tasks Created</h3>
                        <p class="text-gray-600">Create your first task using the form on the left.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

    </div>
</div>

<?php include '../includes/footer.php'; ?>
