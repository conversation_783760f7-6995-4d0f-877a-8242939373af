# AstroGenix - Green Crypto Mining Platform

A comprehensive web-based platform for eco-friendly cryptocurrency mining contracts with automated daily mining rewards calculations.

## 🌟 Features

### User Features
- **User Registration & Authentication** - Secure account creation and login
- **Mining Dashboard** - Track mining contracts performance and earnings
- **Mining Contracts Catalog** - Browse eco-friendly cryptocurrency mining opportunities
- **Detailed Contract Pages** - Complete mining rig information and ROI calculator
- **Deposit/Withdrawal System** - USDT-based funding with admin approval
- **Daily Rewards Tracking** - Automated daily mining rewards based on hash rates
- **Mining Blog** - Cryptocurrency insights and green mining analysis
- **Contact Support** - 24/7 customer support system
- **Referral Program** - Earn bonuses by referring new miners
- **Task System** - Complete daily tasks for additional rewards

### Admin Features
- **Admin Dashboard** - Complete platform overview and statistics
- **User Management** - Monitor and manage user accounts
- **Mining Contract Management** - Add, edit, and manage mining contracts
- **Transaction Processing** - Approve/reject deposits and withdrawals
- **Automated Rewards Distribution** - Daily cron job for mining rewards calculations
- **Referral Management** - Monitor referral trees and bonus distributions
- **Task Management** - Create and manage user tasks and rewards
- **Activity Monitoring** - Track all platform activities

### Technical Features
- **Responsive Design** - Mobile-first approach with Tailwind CSS and green animations
- **Security** - CSRF protection, password hashing, prepared statements
- **Database** - MySQL with comprehensive schema including referral and task systems
- **Cron Jobs** - Automated daily mining rewards calculations
- **Modern UI** - Clean, eco-friendly design with green gradient themes and mining animations

## 🚀 Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Composer (optional, for dependencies)

### Step 1: Database Setup
1. Create a MySQL database named `astrogenix_mining`
2. Import the database schema:
```bash
mysql -u your_username -p astrogenix_mining < database/schema.sql
```

### Step 2: Configuration
1. Update database credentials in `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'poseidon_rental');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

2. Update site URL in `config/config.php`:
```php
define('SITE_URL', 'http://your-domain.com');
```

### Step 3: File Permissions
Set proper permissions for log and upload directories:
```bash
chmod 755 logs/
chmod 755 assets/images/
```

### Step 4: Cron Job Setup
Add this line to your crontab to run daily profit calculations:
```bash
0 0 * * * /usr/bin/php /path/to/your/project/cron/daily_profit.php
```

## 🔐 Default Admin Account

- **Email:** <EMAIL>
- **Password:** admin123

**⚠️ Important:** Change the admin password immediately after installation!

## 📁 Project Structure

```
poseidon/
├── admin/                  # Admin panel
│   ├── index.php          # Admin dashboard
│   ├── users.php          # User management
│   ├── investments.php    # Investment management
│   ├── deposits.php       # Deposit processing
│   └── withdrawals.php    # Withdrawal processing
├── assets/
│   └── images/            # Uploaded images
├── config/
│   ├── database.php       # Database configuration
│   └── config.php         # Application configuration
├── cron/
│   └── daily_profit.php   # Daily profit calculation
├── database/
│   └── schema.sql         # Database schema
├── includes/
│   ├── auth.php           # Authentication functions
│   ├── functions.php      # Core functions
│   ├── header.php         # Page header
│   └── footer.php         # Page footer
├── logs/                  # Application logs
├── pages/
│   ├── login.php          # User login
│   ├── register.php       # User registration
│   ├── dashboard.php      # User dashboard
│   ├── investments.php    # Investment catalog
│   ├── investment-detail.php # Investment details
│   ├── blog.php           # Blog listing
│   └── contact.php        # Contact form
└── index.php              # Homepage
```

## 🎨 Design System

### Color Palette
- **Primary Blue:** #003366
- **Light Blue:** #00BFFF
- **Black:** #000000
- **White:** #FFFFFF

### Typography
- **Primary Font:** Montserrat
- **Fallback:** Roboto, sans-serif

### Components
- Gradient backgrounds and buttons
- Card-based layouts with shadows
- Responsive grid system
- Modern form styling

## 💰 Investment System

### How It Works
1. **User Registration** - Users create accounts and verify email
2. **Deposit Funds** - Users deposit USDT with transaction hash
3. **Admin Approval** - Admin reviews and approves deposits
4. **Browse Investments** - Users explore available opportunities
5. **Make Investment** - Users invest from their balance
6. **Daily Profits** - Automated daily profit calculations
7. **Withdraw Funds** - Users request withdrawals (admin approval)

### Profit Calculation
- **Daily Rate:** Monthly Rate ÷ 30
- **Daily Profit:** Investment Amount × (Daily Rate ÷ 100)
- **Monthly Profit:** Investment Amount × (Monthly Rate ÷ 100)

## 🔧 Configuration Options

### Investment Settings
```php
define('MIN_INVESTMENT_AMOUNT', 100);
define('MAX_INVESTMENT_AMOUNT', 1000000);
```

### Security Settings
```php
define('SESSION_LIFETIME', 3600 * 24); // 24 hours
define('CSRF_TOKEN_NAME', 'csrf_token');
```

### File Upload Settings
```php
define('UPLOAD_PATH', 'assets/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
```

## 🛡️ Security Features

- **Password Hashing** - Using PHP's password_hash()
- **CSRF Protection** - Token-based form protection
- **SQL Injection Prevention** - Prepared statements
- **Input Sanitization** - All user inputs sanitized
- **Session Security** - Secure session management
- **Admin Access Control** - Role-based permissions

## 📱 Mobile Responsiveness

The platform is fully responsive and optimized for:
- **Mobile Phones** (320px+)
- **Tablets** (768px+)
- **Desktop** (1024px+)
- **Large Screens** (1440px+)

## 🔄 Cron Jobs

### Daily Profit Calculation
- **File:** `cron/daily_profit.php`
- **Schedule:** Daily at midnight
- **Function:** Calculate and distribute daily profits
- **Logging:** Detailed logs in `logs/` directory

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running

2. **Permission Denied**
   - Set proper file permissions: `chmod 755 logs/ assets/`

3. **Cron Job Not Running**
   - Check crontab configuration
   - Verify PHP path: `which php`

4. **Images Not Loading**
   - Check file permissions in `assets/images/`
   - Verify upload directory exists

## 📞 Support

For technical support or questions:
- **Email:** <EMAIL>
- **Documentation:** Check this README
- **Issues:** Create GitHub issues for bugs

## 📄 License

This project is proprietary software. All rights reserved.

## 🚀 Deployment

### Production Checklist
- [ ] Change default admin password
- [ ] Update database credentials
- [ ] Set proper file permissions
- [ ] Configure SSL certificate
- [ ] Set up cron jobs
- [ ] Configure email settings
- [ ] Test all functionality
- [ ] Set up backups

### Performance Optimization
- Enable PHP OPcache
- Use CDN for static assets
- Optimize database queries
- Implement caching where needed
- Compress images

---

**Poseidon Rental Pro** - Luxury Investment Platform
© 2024 All Rights Reserved
