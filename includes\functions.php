<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';

// User functions
function getUserById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function getUserByEmail($email) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

function createUser($username, $email, $password, $referred_by = null) {
    $db = getDB();
    $password_hash = password_hash($password, PASSWORD_DEFAULT);

    $stmt = $db->prepare("INSERT INTO users (username, email, password_hash, referred_by) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$username, $email, $password_hash, $referred_by]);
}

function updateUserBalance($user_id, $amount, $operation = 'add') {
    $db = getDB();
    
    if ($operation === 'add') {
        $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
    } else {
        $stmt = $db->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
    }
    
    return $stmt->execute([$amount, $user_id]);
}

// Investment functions
function getAllInvestments($active_only = true) {
    $db = getDB();
    $sql = "SELECT * FROM investments";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY created_at DESC";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $investments = $stmt->fetchAll();

    // Add compatibility fields for each investment
    foreach ($investments as &$investment) {
        $investment = addInvestmentCompatibilityFields($investment);
    }

    return $investments;
}

function getInvestmentById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM investments WHERE id = ?");
    $stmt->execute([$id]);
    $investment = $stmt->fetch();

    // Add compatibility fields for old code
    if ($investment) {
        $investment = addInvestmentCompatibilityFields($investment);
    }

    return $investment;
}

// Add compatibility fields for investments to work with old frontend code
function addInvestmentCompatibilityFields($investment) {
    if (!$investment) return $investment;

    // Add missing fields with default values or computed values
    $investment['price'] = $investment['max_amount'] ?? $investment['min_amount'];
    $investment['monthly_rate_min'] = $investment['monthly_rate'];
    $investment['monthly_rate_max'] = $investment['monthly_rate'];
    $investment['return_period_months'] = $investment['duration_months'] ?? 12;

    // Ensure required fields exist with defaults
    if (!isset($investment['location'])) {
        $investment['location'] = ucfirst(str_replace('_', ' ', $investment['category']));
    }
    if (!isset($investment['capital_return'])) {
        $investment['capital_return'] = true;
    }
    if (!isset($investment['features'])) {
        $investment['features'] = '';
    }

    return $investment;
}

function createUserInvestment($user_id, $investment_id, $amount, $monthly_rate, $return_period_months = null) {
    $db = getDB();

    // Get investment details to determine duration if not provided
    if ($return_period_months === null) {
        $investment = getInvestmentById($investment_id);
        $return_period_months = $investment['duration_months'] ?? 12;
    }

    $start_date = date('Y-m-d');
    $end_date = date('Y-m-d', strtotime("+{$return_period_months} months"));

    $stmt = $db->prepare("
        INSERT INTO user_investments (user_id, investment_id, amount, monthly_rate, start_date, end_date)
        VALUES (?, ?, ?, ?, ?, ?)
    ");

    return $stmt->execute([$user_id, $investment_id, $amount, $monthly_rate, $start_date, $end_date]);
}

function getUserInvestments($user_id) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT ui.*, i.title, i.category, i.image_url 
        FROM user_investments ui 
        JOIN investments i ON ui.investment_id = i.id 
        WHERE ui.user_id = ? 
        ORDER BY ui.created_at DESC
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

// Transaction functions
function createTransaction($user_id, $type, $amount, $screenshot_path = null, $wallet_address = null) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, screenshot_path, wallet_address, status)
        VALUES (?, ?, ?, ?, ?, 'pending')
    ");

    return $stmt->execute([$user_id, $type, $amount, $screenshot_path, $wallet_address]);
}

function getUserTransactions($user_id, $limit = 10) {
    $db = getDB();

    // Ensure limit is an integer and within reasonable bounds
    $limit = max(1, min(100, intval($limit)));

    // Get regular transactions and daily profits combined
    $stmt = $db->prepare("
        (SELECT
            id,
            'transaction' as source_type,
            type,
            amount,
            status,
            created_at,
            wallet_address,
            screenshot_path,
            admin_notes
        FROM transactions
        WHERE user_id = ?)
        UNION ALL
        (SELECT
            dp.id,
            'profit' as source_type,
            'profit_credit' as type,
            dp.profit_amount as amount,
            'completed' as status,
            dp.created_at,
            NULL as wallet_address,
            NULL as screenshot_path,
            CONCAT('Daily profit from investment #', ui.investment_id) as admin_notes
        FROM daily_profits dp
        JOIN user_investments ui ON dp.user_investment_id = ui.id
        WHERE ui.user_id = ?)
        ORDER BY created_at DESC
        LIMIT " . $limit
    );
    $stmt->execute([$user_id, $user_id]);
    return $stmt->fetchAll();
}

function getPendingTransactions($type = null) {
    $db = getDB();
    $sql = "
        SELECT t.*, u.username, u.email 
        FROM transactions t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.status = 'pending'
    ";
    
    if ($type) {
        $sql .= " AND t.type = ?";
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute([$type]);
    } else {
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute();
    }
    
    return $stmt->fetchAll();
}

function updateTransactionStatus($transaction_id, $status, $admin_notes = null) {
    $db = getDB();
    $stmt = $db->prepare("
        UPDATE transactions 
        SET status = ?, admin_notes = ?, processed_at = NOW() 
        WHERE id = ?
    ");
    
    return $stmt->execute([$status, $admin_notes, $transaction_id]);
}

// Daily profit calculation
function calculateDailyProfit($user_investment_id, $amount, $monthly_rate) {
    $daily_rate = $monthly_rate / 30;
    return ($amount * $daily_rate) / 100;
}

function addDailyProfit($user_investment_id, $profit_amount, $date = null) {
    $db = getDB();
    
    if (!$date) {
        $date = date('Y-m-d');
    }
    
    $stmt = $db->prepare("
        INSERT IGNORE INTO daily_profits (user_investment_id, profit_amount, profit_date) 
        VALUES (?, ?, ?)
    ");
    
    return $stmt->execute([$user_investment_id, $profit_amount, $date]);
}

// Blog functions
function getBlogPosts($published_only = true, $limit = null) {
    $db = getDB();
    $sql = "SELECT * FROM blog_posts";
    
    if ($published_only) {
        $sql .= " WHERE is_published = 1";
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

function getBlogPostBySlug($slug) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE slug = ? AND is_published = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Contact functions
function saveContactMessage($name, $email, $subject, $message) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO contact_messages (name, email, subject, message) 
        VALUES (?, ?, ?, ?)
    ");
    
    return $stmt->execute([$name, $email, $subject, $message]);
}

// Statistics functions
function getDashboardStats($user_id) {
    $db = getDB();

    // Get user balance
    $user = getUserById($user_id);
    $balance = $user['balance'] ?? 0;

    // Get total invested amount
    $stmt = $db->prepare("SELECT SUM(amount) as total_invested FROM user_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $total_invested = $stmt->fetch()['total_invested'] ?? 0;

    // Get total profit earned
    $stmt = $db->prepare("
        SELECT SUM(dp.profit_amount) as total_profit
        FROM daily_profits dp
        JOIN user_investments ui ON dp.user_investment_id = ui.id
        WHERE ui.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $total_profit = $stmt->fetch()['total_profit'] ?? 0;

    // Get active investments count
    $stmt = $db->prepare("SELECT COUNT(*) as active_investments FROM user_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $active_investments = $stmt->fetch()['active_investments'] ?? 0;

    return [
        'balance' => floatval($balance),
        'total_invested' => floatval($total_invested),
        'total_profit' => floatval($total_profit),
        'active_investments' => intval($active_investments)
    ];
}

// Withdrawal processing function

function processWithdrawalRequest($user_id, $amount, $wallet_address) {
    $user = getUserById($user_id);
    $balance = $user['balance'] ?? 0;

    if ($amount < 10) {
        return ['success' => false, 'message' => 'Minimum withdrawal amount is $10'];
    }

    if ($amount > $balance) {
        return ['success' => false, 'message' => 'Insufficient balance'];
    }

    if (empty($wallet_address)) {
        return ['success' => false, 'message' => 'Wallet address is required'];
    }

    // Create withdrawal transaction directly with new schema
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, wallet_address, status)
        VALUES (?, 'withdrawal', ?, ?, 'pending')
    ");

    if ($stmt->execute([$user_id, $amount, $wallet_address])) {
        return ['success' => true, 'message' => 'Withdrawal request submitted successfully. It will be processed within 24-48 hours.'];
    } else {
        return ['success' => false, 'message' => 'Failed to submit withdrawal request. Please try again.'];
    }
}

// Formatting functions
function formatCurrency($amount) {
    return '$' . number_format(floatval($amount), 2);
}

function formatPercentage($percentage) {
    return number_format(floatval($percentage), 2);
}

// CSRF Protection functions
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Verification functions
function getUserVerificationStatus($user_id) {
    $user = getUserById($user_id);
    return $user['verification_status'] ?? 'unverified';
}

function isUserVerified($user_id) {
    return getUserVerificationStatus($user_id) === 'verified';
}

function getUserVerification($user_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM user_verifications WHERE user_id = ? ORDER BY submitted_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

function submitVerificationRequest($user_id, $first_name, $last_name, $birth_date, $passport_file) {
    // Upload passport photo
    $uploadResult = uploadPassportPhoto($passport_file);

    if (!$uploadResult['success']) {
        return $uploadResult;
    }

    $db = getDB();

    try {
        $db->beginTransaction();

        // Insert verification request
        $stmt = $db->prepare("
            INSERT INTO user_verifications (user_id, first_name, last_name, birth_date, passport_photo_path, status)
            VALUES (?, ?, ?, ?, ?, 'pending')
        ");

        if (!$stmt->execute([$user_id, $first_name, $last_name, $birth_date, $uploadResult['file_path']])) {
            throw new Exception('Failed to insert verification request');
        }

        // Update user verification status
        $stmt = $db->prepare("UPDATE users SET verification_status = 'pending' WHERE id = ?");
        if (!$stmt->execute([$user_id])) {
            throw new Exception('Failed to update user verification status');
        }

        $db->commit();

        return ['success' => true, 'message' => 'Verification request submitted successfully. Please wait for admin approval.'];

    } catch (Exception $e) {
        $db->rollback();

        // Delete uploaded file if database insert fails
        $fullPath = $uploadResult['full_path'] ?? null;
        if ($fullPath && file_exists($fullPath)) {
            unlink($fullPath);
        }

        logError("Verification submission error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to submit verification request. Please try again.'];
    }
}

function uploadPassportPhoto($file) {
    $uploadDir = 'uploads/verifications/';

    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Please upload JPG, PNG, or GIF image.'];
    }

    if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
        return ['success' => false, 'message' => 'File size too large. Maximum size is 5MB.'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'passport_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $fullPath = __DIR__ . '/../' . $filepath;

    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        return [
            'success' => true,
            'file_path' => $filepath,
            'full_path' => $fullPath
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to upload file'];
    }
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}



// Referral statistics function
function getUserReferralStats($user_id) {
    $db = getDB();

    // Get total referrals count
    $stmt = $db->prepare("SELECT COUNT(*) as total_referrals FROM users WHERE referred_by = ?");
    $stmt->execute([$user_id]);
    $total_referrals = $stmt->fetch()['total_referrals'];

    // Get total earnings
    $stmt = $db->prepare("SELECT COALESCE(SUM(amount), 0) as total_earnings FROM referral_earnings WHERE referrer_id = ?");
    $stmt->execute([$user_id]);
    $total_earnings = $stmt->fetch()['total_earnings'];

    // Get this month's referrals
    $stmt = $db->prepare("
        SELECT COUNT(*) as month_referrals
        FROM users
        WHERE referred_by = ?
        AND MONTH(created_at) = MONTH(CURRENT_DATE())
        AND YEAR(created_at) = YEAR(CURRENT_DATE())
    ");
    $stmt->execute([$user_id]);
    $month_referrals = $stmt->fetch()['month_referrals'];

    // Get this month's earnings
    $stmt = $db->prepare("
        SELECT COALESCE(SUM(amount), 0) as month_earnings
        FROM referral_earnings
        WHERE referrer_id = ?
        AND MONTH(created_at) = MONTH(CURRENT_DATE())
        AND YEAR(created_at) = YEAR(CURRENT_DATE())
    ");
    $stmt->execute([$user_id]);
    $month_earnings = $stmt->fetch()['month_earnings'];

    return [
        'total_referrals' => $total_referrals,
        'total_earnings' => $total_earnings,
        'month_referrals' => $month_referrals,
        'month_earnings' => $month_earnings
    ];
}

// Task statistics function
function getUserTaskStats($user_id) {
    $db = getDB();

    // Get total completed tasks
    $stmt = $db->prepare("SELECT COUNT(*) as completed_tasks FROM user_tasks WHERE user_id = ? AND is_completed = 1");
    $stmt->execute([$user_id]);
    $completed_tasks = $stmt->fetch()['completed_tasks'];

    // Get total earned from tasks
    $stmt = $db->prepare("
        SELECT COALESCE(SUM(t.reward_amount), 0) as total_earned
        FROM user_tasks ut
        JOIN tasks t ON ut.task_id = t.id
        WHERE ut.user_id = ? AND ut.reward_claimed = 1
    ");
    $stmt->execute([$user_id]);
    $total_earned = $stmt->fetch()['total_earned'];

    // Get pending rewards
    $stmt = $db->prepare("
        SELECT COALESCE(SUM(t.reward_amount), 0) as pending_rewards
        FROM user_tasks ut
        JOIN tasks t ON ut.task_id = t.id
        WHERE ut.user_id = ? AND ut.is_completed = 1 AND ut.reward_claimed = 0
    ");
    $stmt->execute([$user_id]);
    $pending_rewards = $stmt->fetch()['pending_rewards'];

    // Get available tasks count
    $stmt = $db->prepare("
        SELECT COUNT(*) as available_tasks
        FROM tasks t
        WHERE t.is_active = 1
        AND t.id NOT IN (
            SELECT task_id FROM user_tasks WHERE user_id = ?
        )
    ");
    $stmt->execute([$user_id]);
    $available_tasks = $stmt->fetch()['available_tasks'];

    return [
        'completed_tasks' => $completed_tasks,
        'total_earned' => $total_earned,
        'pending_rewards' => $pending_rewards,
        'available_tasks' => $available_tasks
    ];
}

// Get recent referrals function
function getRecentReferrals($user_id, $limit = 10) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT username, email, created_at
        FROM users
        WHERE referred_by = ?
        ORDER BY created_at DESC
        LIMIT ?
    ");
    $stmt->execute([$user_id, $limit]);
    return $stmt->fetchAll();
}

// Get user referral code function
function getUserReferralCode($user_id) {
    $db = getDB();

    // Check if user already has a referral code
    $stmt = $db->prepare("SELECT code FROM referral_codes WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $existing_code = $stmt->fetch();

    if ($existing_code) {
        return $existing_code['code'];
    }

    // Generate new unique referral code
    do {
        $code = 'AG' . strtoupper(substr(md5(uniqid()), 0, 8));
        $stmt = $db->prepare("SELECT id FROM referral_codes WHERE code = ?");
        $stmt->execute([$code]);
    } while ($stmt->fetch());

    // Insert new referral code
    $stmt = $db->prepare("INSERT INTO referral_codes (user_id, code) VALUES (?, ?)");
    $stmt->execute([$user_id, $code]);

    return $code;
}

// Get user tasks with progress function
function getUserTasksWithProgress($user_id) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT
            t.*,
            ut.is_completed,
            ut.completed_at,
            ut.reward_claimed,
            CASE
                WHEN ut.id IS NULL THEN 'available'
                WHEN ut.is_completed = 0 THEN 'in_progress'
                WHEN ut.is_completed = 1 AND ut.reward_claimed = 0 THEN 'completed'
                WHEN ut.is_completed = 1 AND ut.reward_claimed = 1 THEN 'claimed'
                ELSE 'available'
            END as status
        FROM tasks t
        LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
        WHERE t.is_active = 1
        ORDER BY t.category, t.sort_order, t.id
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}



// Flash message functions
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash_messages'][$type])) {
        $message = $_SESSION['flash_messages'][$type];
        unset($_SESSION['flash_messages'][$type]);
        return $message;
    }
    return null;
}

function displayFlashMessages() {
    $success_message = getFlashMessage('success');
    $error_message = getFlashMessage('error');
    $info_message = getFlashMessage('info');

    if ($success_message): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif;

    if ($error_message): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif;

    if ($info_message): ?>
        <div class="mb-6 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-info-circle mr-2"></i><?php echo htmlspecialchars($info_message); ?>
        </div>
    <?php endif;
}

// Redirect function
function redirect($url) {
    header("Location: $url");
    exit();
}

// Blog image functions
function getBlogPostImage($post, $size = 'medium') {
    // Default fallback image
    $default_image = 'https://lim-english.com/uploads/images/all/img_articles_new/news_in_english.jpg';

    // Check if post has image_url key and it's not empty
    if (isset($post['image_url']) && !empty($post['image_url'])) {
        return $post['image_url'];
    }

    return $default_image;
}

function getEscapedBlogImage($post, $size = 'medium') {
    return htmlspecialchars(getBlogPostImage($post, $size));
}

// Investment image functions
function getInvestmentImage($investment, $size = 'medium') {
    // Default fallback image for investments
    $default_image = 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&amp;auto=format&amp;fit=crop&amp;w=600&amp;q=80';

    // Check if investment has image_url key and it's not empty
    if (isset($investment['image_url']) && !empty($investment['image_url'])) {
        return $investment['image_url'];
    }

    return $default_image;
}

function getEscapedInvestmentImage($investment, $size = 'medium') {
    return htmlspecialchars(getInvestmentImage($investment, $size));
}

// User status functions
function getUserStatus($user) {
    // Check for new status field first, then fallback to legacy is_suspended
    if (isset($user['status'])) {
        return $user['status'];
    } elseif (isset($user['is_suspended']) && $user['is_suspended']) {
        return 'suspended';
    } else {
        return 'active';
    }
}

function getUserRole($user) {
    // Check for new role field first, then fallback to legacy is_admin
    if (isset($user['role'])) {
        return $user['role'];
    } elseif (isset($user['is_admin']) && $user['is_admin']) {
        return 'admin';
    } else {
        return 'user';
    }
}

function isUserSuspended($user) {
    return getUserStatus($user) === 'suspended';
}

function isUserAdmin($user) {
    return getUserRole($user) === 'admin';
}

// File upload functions
function uploadTransactionScreenshot($file) {
    // Use absolute path from document root
    $uploadDir = __DIR__ . '/../uploads/transactions/';
    $webPath = 'uploads/transactions/';
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    // Enhanced logging for debugging
    error_log("=== uploadTransactionScreenshot DEBUG ===");
    error_log("Upload dir: $uploadDir");
    error_log("File data: " . print_r($file, true));

    // Validate file
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        error_log("No tmp_name in file array");
        return ['success' => false, 'message' => 'No file uploaded - missing tmp_name'];
    }

    if (!is_uploaded_file($file['tmp_name'])) {
        error_log("File is not an uploaded file: " . $file['tmp_name']);
        return ['success' => false, 'message' => 'Invalid uploaded file'];
    }

    if ($file['error'] !== UPLOAD_ERR_OK) {
        error_log("Upload error: " . $file['error']);
        return ['success' => false, 'message' => 'File upload error: ' . $file['error']];
    }

    if ($file['size'] > $maxSize) {
        error_log("File too large: " . $file['size']);
        return ['success' => false, 'message' => 'File size exceeds 5MB limit'];
    }

    if (!in_array($file['type'], $allowedTypes)) {
        error_log("Invalid file type: " . $file['type']);
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed'];
    }

    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            error_log("Failed to create upload directory: $uploadDir");
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
        error_log("Created upload directory: $uploadDir");
    }

    // Generate unique filename
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = uniqid('transaction_') . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $webFilepath = $webPath . $filename;

    error_log("Attempting to move file from {$file['tmp_name']} to $filepath");

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        error_log("File uploaded successfully to: $filepath");
        return ['success' => true, 'filepath' => $webFilepath, 'full_path' => $filepath];
    } else {
        error_log("Failed to move uploaded file");
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

// Process deposit request with screenshot
function processDepositRequest($user_id, $amount, $screenshot_file) {
    error_log("=== processDepositRequest DEBUG ===");
    error_log("User ID: $user_id");
    error_log("Amount: $amount");
    error_log("Screenshot file: " . print_r($screenshot_file, true));

    if ($amount < 10) {
        error_log("Amount too small: $amount");
        return ['success' => false, 'message' => 'Minimum deposit amount is $10'];
    }

    // Upload screenshot
    $uploadResult = uploadTransactionScreenshot($screenshot_file);
    error_log("Upload result: " . print_r($uploadResult, true));

    if (!$uploadResult['success']) {
        return $uploadResult;
    }

    try {
        $db = getDB();
        $stmt = $db->prepare("
            INSERT INTO transactions (user_id, type, amount, screenshot_path, status, created_at)
            VALUES (?, 'deposit', ?, ?, 'pending', NOW())
        ");

        $filepath = $uploadResult['filepath'];
        error_log("Inserting transaction with filepath: $filepath");

        if ($stmt->execute([$user_id, $amount, $filepath])) {
            $transaction_id = $db->lastInsertId();
            error_log("Transaction inserted successfully with ID: $transaction_id");
            return ['success' => true, 'message' => 'Deposit request submitted successfully. Please wait for admin approval.', 'transaction_id' => $transaction_id];
        } else {
            error_log("Failed to insert transaction");
            error_log("SQL Error: " . print_r($stmt->errorInfo(), true));

            // Delete uploaded file if database insert fails
            $fullPath = $uploadResult['full_path'] ?? null;
            if ($fullPath && file_exists($fullPath)) {
                unlink($fullPath);
                error_log("Deleted uploaded file due to DB error: $fullPath");
            }
            return ['success' => false, 'message' => 'Failed to submit deposit request. Please try again.'];
        }
    } catch (Exception $e) {
        error_log("Exception in processDepositRequest: " . $e->getMessage());

        // Delete uploaded file if exception occurs
        $fullPath = $uploadResult['full_path'] ?? null;
        if ($fullPath && file_exists($fullPath)) {
            unlink($fullPath);
            error_log("Deleted uploaded file due to exception: $fullPath");
        }
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Site settings functions
function getSiteSetting($key, $default = '') {
    $db = getDB();
    $stmt = $db->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

function updateSiteSetting($key, $value, $type = 'text') {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO site_settings (setting_key, setting_value, setting_type)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP
    ");
    return $stmt->execute([$key, $value, $type, $value]);
}

function getAllSiteSettings() {
    $db = getDB();
    $stmt = $db->query("SELECT * FROM site_settings ORDER BY setting_key");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}

// Referral system functions
function generateReferralCode($user_id) {
    $db = getDB();

    // Check if user already has a referral code
    $stmt = $db->prepare("SELECT code FROM referral_codes WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $existing = $stmt->fetch();

    if ($existing) {
        return $existing['code'];
    }

    // Generate unique referral code
    do {
        $code = 'ASTRO' . strtoupper(substr(uniqid(), -6));
        $stmt = $db->prepare("SELECT id FROM referral_codes WHERE code = ?");
        $stmt->execute([$code]);
    } while ($stmt->fetch());

    // Insert new referral code
    $stmt = $db->prepare("INSERT INTO referral_codes (user_id, code) VALUES (?, ?)");
    if ($stmt->execute([$user_id, $code])) {
        return $code;
    }

    return false;
}

function getReferralCodeByUser($user_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT code FROM referral_codes WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    return $result ? $result['code'] : null;
}

function getUserByReferralCode($code) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT u.* FROM users u
        JOIN referral_codes rc ON u.id = rc.user_id
        WHERE rc.code = ?
    ");
    $stmt->execute([$code]);
    return $stmt->fetch();
}

function addReferralEarning($referrer_id, $referred_id, $amount, $type = 'signup_bonus') {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO referral_earnings (referrer_id, referred_id, amount, type)
        VALUES (?, ?, ?, ?)
    ");
    return $stmt->execute([$referrer_id, $referred_id, $amount, $type]);
}

function getReferralEarnings($user_id) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT re.*, u.username as referred_username
        FROM referral_earnings re
        JOIN users u ON re.referred_id = u.id
        WHERE re.referrer_id = ?
        ORDER BY re.created_at DESC
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

function getReferralStats($user_id) {
    $db = getDB();

    // Get total referrals count
    $stmt = $db->prepare("SELECT COUNT(*) as total_referrals FROM users WHERE referred_by = ?");
    $stmt->execute([$user_id]);
    $total_referrals = $stmt->fetch()['total_referrals'] ?? 0;

    // Get total earnings
    $stmt = $db->prepare("SELECT SUM(amount) as total_earnings FROM referral_earnings WHERE referrer_id = ?");
    $stmt->execute([$user_id]);
    $total_earnings = $stmt->fetch()['total_earnings'] ?? 0;

    // Get this month's referrals
    $stmt = $db->prepare("
        SELECT COUNT(*) as month_referrals
        FROM users
        WHERE referred_by = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())
    ");
    $stmt->execute([$user_id]);
    $month_referrals = $stmt->fetch()['month_referrals'] ?? 0;

    return [
        'total_referrals' => intval($total_referrals),
        'total_earnings' => floatval($total_earnings),
        'month_referrals' => intval($month_referrals)
    ];
}

function processReferralBonus($referred_user_id) {
    $db = getDB();

    // Get referred user info
    $referred_user = getUserById($referred_user_id);
    if (!$referred_user || !$referred_user['referred_by']) {
        return false;
    }

    $referrer_id = $referred_user['referred_by'];
    $signup_bonus = 5.00; // $5 signup bonus

    try {
        $db->beginTransaction();

        // Add bonus to referrer's balance
        updateUserBalance($referrer_id, $signup_bonus, 'add');

        // Record the referral earning
        addReferralEarning($referrer_id, $referred_user_id, $signup_bonus, 'signup_bonus');

        $db->commit();
        return true;

    } catch (Exception $e) {
        $db->rollback();
        logError("Referral bonus processing error: " . $e->getMessage());
        return false;
    }
}

// Advanced referral functions for dashboard
function getReferralDetails($user_id) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT u.id, u.username, u.email, u.created_at, u.balance,
               COALESCE(SUM(ui.amount), 0) as total_invested
        FROM users u
        LEFT JOIN user_investments ui ON u.id = ui.user_id AND ui.is_active = 1
        WHERE u.referred_by = ?
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

function getReferralEarningsByMonth($user_id, $months = 12) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT
            DATE_FORMAT(created_at, '%Y-%m') as month,
            SUM(amount) as total_earnings,
            COUNT(*) as referral_count
        FROM referral_earnings
        WHERE referrer_id = ?
        AND created_at >= DATE_SUB(CURRENT_DATE, INTERVAL ? MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month DESC
    ");
    $stmt->execute([$user_id, $months]);
    return $stmt->fetchAll();
}

function getTopReferrers($limit = 10) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT
            u.username,
            COUNT(r.id) as total_referrals,
            COALESCE(SUM(re.amount), 0) as total_earnings
        FROM users u
        LEFT JOIN users r ON u.id = r.referred_by
        LEFT JOIN referral_earnings re ON u.id = re.referrer_id
        GROUP BY u.id
        HAVING total_referrals > 0
        ORDER BY total_referrals DESC, total_earnings DESC
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}

function processInvestmentReferralBonus($user_investment_id, $investment_amount) {
    $db = getDB();

    // Get user investment details
    $stmt = $db->prepare("
        SELECT ui.user_id, u.referred_by
        FROM user_investments ui
        JOIN users u ON ui.user_id = u.id
        WHERE ui.id = ?
    ");
    $stmt->execute([$user_investment_id]);
    $result = $stmt->fetch();

    if (!$result || !$result['referred_by']) {
        return false;
    }

    $referrer_id = $result['referred_by'];
    $referred_user_id = $result['user_id'];

    // Calculate 2% commission on investment
    $commission_rate = 0.02; // 2%
    $commission_amount = $investment_amount * $commission_rate;

    try {
        $db->beginTransaction();

        // Add commission to referrer's balance
        updateUserBalance($referrer_id, $commission_amount, 'add');

        // Record the referral earning
        addReferralEarning($referrer_id, $referred_user_id, $commission_amount, 'investment_commission');

        $db->commit();
        return true;

    } catch (Exception $e) {
        $db->rollback();
        logError("Investment referral bonus error: " . $e->getMessage());
        return false;
    }
}

// Task system functions
function getAllTasks($active_only = true) {
    $db = getDB();
    $sql = "SELECT * FROM tasks";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY category, sort_order, id";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

function getTaskById($task_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM tasks WHERE id = ?");
    $stmt->execute([$task_id]);
    return $stmt->fetch();
}

function getUserTaskProgress($user_id, $task_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM user_tasks WHERE user_id = ? AND task_id = ?");
    $stmt->execute([$user_id, $task_id]);
    return $stmt->fetch();
}

function getUserTasks($user_id, $category = null) {
    $db = getDB();
    $sql = "
        SELECT t.*, ut.completed_at, ut.progress, ut.is_completed, ut.reward_claimed
        FROM tasks t
        LEFT JOIN user_tasks ut ON t.id = ut.task_id AND ut.user_id = ?
        WHERE t.is_active = 1
    ";

    if ($category) {
        $sql .= " AND t.category = ?";
        $stmt = $db->prepare($sql . " ORDER BY t.sort_order, t.id");
        $stmt->execute([$user_id, $category]);
    } else {
        $stmt = $db->prepare($sql . " ORDER BY t.category, t.sort_order, t.id");
        $stmt->execute([$user_id]);
    }

    return $stmt->fetchAll();
}

function completeTask($user_id, $task_id) {
    $db = getDB();

    // Check if task exists and is active
    $task = getTaskById($task_id);
    if (!$task || !$task['is_active']) {
        return ['success' => false, 'message' => 'Task not found or inactive'];
    }

    // Check if already completed
    $progress = getUserTaskProgress($user_id, $task_id);
    if ($progress && $progress['is_completed']) {
        return ['success' => false, 'message' => 'Task already completed'];
    }

    try {
        $db->beginTransaction();

        // Mark task as completed
        if ($progress) {
            $stmt = $db->prepare("
                UPDATE user_tasks
                SET is_completed = 1, completed_at = NOW(), progress = 100
                WHERE user_id = ? AND task_id = ?
            ");
            $stmt->execute([$user_id, $task_id]);
        } else {
            $stmt = $db->prepare("
                INSERT INTO user_tasks (user_id, task_id, is_completed, completed_at, progress)
                VALUES (?, ?, 1, NOW(), 100)
            ");
            $stmt->execute([$user_id, $task_id]);
        }

        $db->commit();
        return ['success' => true, 'message' => 'Task completed successfully'];

    } catch (Exception $e) {
        $db->rollback();
        logError("Task completion error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to complete task'];
    }
}

function claimTaskReward($user_id, $task_id) {
    $db = getDB();

    // Check if task is completed but reward not claimed
    $progress = getUserTaskProgress($user_id, $task_id);
    if (!$progress || !$progress['is_completed'] || $progress['reward_claimed']) {
        return ['success' => false, 'message' => 'Task not completed or reward already claimed'];
    }

    $task = getTaskById($task_id);
    if (!$task) {
        return ['success' => false, 'message' => 'Task not found'];
    }

    try {
        $db->beginTransaction();

        // Add reward to user balance
        updateUserBalance($user_id, $task['reward_amount'], 'add');

        // Mark reward as claimed
        $stmt = $db->prepare("
            UPDATE user_tasks
            SET reward_claimed = 1, reward_claimed_at = NOW()
            WHERE user_id = ? AND task_id = ?
        ");
        $stmt->execute([$user_id, $task_id]);

        $db->commit();
        return ['success' => true, 'message' => 'Reward claimed successfully', 'amount' => $task['reward_amount']];

    } catch (Exception $e) {
        $db->rollback();
        logError("Task reward claim error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to claim reward'];
    }
}

function getTaskStats($user_id) {
    $db = getDB();

    // Get total tasks
    $stmt = $db->prepare("SELECT COUNT(*) as total_tasks FROM tasks WHERE is_active = 1");
    $stmt->execute();
    $total_tasks = $stmt->fetch()['total_tasks'] ?? 0;

    // Get completed tasks
    $stmt = $db->prepare("
        SELECT COUNT(*) as completed_tasks
        FROM user_tasks ut
        JOIN tasks t ON ut.task_id = t.id
        WHERE ut.user_id = ? AND ut.is_completed = 1 AND t.is_active = 1
    ");
    $stmt->execute([$user_id]);
    $completed_tasks = $stmt->fetch()['completed_tasks'] ?? 0;

    // Get total rewards earned
    $stmt = $db->prepare("
        SELECT SUM(t.reward_amount) as total_rewards
        FROM user_tasks ut
        JOIN tasks t ON ut.task_id = t.id
        WHERE ut.user_id = ? AND ut.reward_claimed = 1 AND t.is_active = 1
    ");
    $stmt->execute([$user_id]);
    $total_rewards = $stmt->fetch()['total_rewards'] ?? 0;

    return [
        'total_tasks' => intval($total_tasks),
        'completed_tasks' => intval($completed_tasks),
        'completion_rate' => $total_tasks > 0 ? round(($completed_tasks / $total_tasks) * 100, 1) : 0,
        'total_rewards' => floatval($total_rewards)
    ];
}
?>
