# AstroGenix Installation Guide

## 🚀 Quick Installation

AstroGenix comes with an automated web-based installer that will guide you through the entire setup process.

### Prerequisites

Before starting the installation, ensure your server meets these requirements:

#### Server Requirements
- **PHP**: 7.4 or higher
- **MySQL**: 5.7+ or MariaDB 10.2+
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **SSL Certificate**: Recommended for production

#### PHP Extensions Required
- PDO MySQL
- Mbstring
- OpenSSL
- cURL
- GD
- JSON (usually enabled by default)

#### File Permissions
- `config/` directory must be writable
- `assets/` directory must be writable
- `logs/` directory will be created automatically

### Installation Steps

1. **Upload Files**
   ```bash
   # Upload all files to your web server directory
   # Example: /var/www/html/astrogenix/ or /public_html/astrogenix/
   ```

2. **Set Permissions**
   ```bash
   chmod 755 config/
   chmod 755 assets/
   chmod 644 install.php
   ```

3. **Run Web Installer**
   - Open your browser and navigate to: `https://yourdomain.com/astrogenix/install.php`
   - Follow the 7-step installation wizard

4. **Delete Installation Files**
   ```bash
   rm install.php
   rm -rf install/
   ```

## 📋 Installation Wizard Steps

### Step 1: System Requirements Check
- Automatically checks PHP version and extensions
- Verifies file permissions
- Shows any issues that need to be resolved

### Step 2: Database Configuration
- Enter your database connection details
- Tests the connection before proceeding
- Creates database configuration file

### Step 3: Database Creation
- Creates the database (if it doesn't exist)
- Creates all required tables
- Inserts sample data and default settings

### Step 4: Admin Account Setup
- Create your administrator account
- Set admin username, email, and password
- This account will have full access to the admin panel

### Step 5: File Permissions
- Automatically sets up required directories
- Configures proper file permissions
- Creates logs directory

### Step 6: Final Configuration
- Generates security keys
- Updates site settings
- Creates installation lock file

### Step 7: Installation Complete
- Shows installation summary
- Provides access links
- Security recommendations

## 🔧 Manual Installation (Advanced)

If you prefer manual installation or the web installer doesn't work:

### 1. Database Setup
```sql
-- Create database
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
mysql -u username -p astrogenix < database/install_schema.sql
```

### 2. Configuration Files
```php
// Copy and edit config/database.php
cp config/database.php.template config/database.php

// Edit database credentials
define('DB_HOST', 'localhost');
define('DB_NAME', 'astrogenix');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. Create Admin User
```sql
INSERT INTO users (username, email, password_hash, is_admin, balance) 
VALUES ('admin', '<EMAIL>', '$2y$10$hash_here', 1, 0);
```

### 4. Set Permissions
```bash
mkdir logs
chmod 755 logs
chmod 755 config
chmod 755 assets
```

## 🌐 Web Server Configuration

### Apache (.htaccess)
The included `.htaccess` file handles:
- URL rewriting
- Security headers
- File access restrictions
- Compression

### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /var/www/html/astrogenix;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\. {
        deny all;
    }
}
```

## 🔒 Security Configuration

### SSL Certificate
```bash
# Using Let's Encrypt
certbot --nginx -d yourdomain.com
```

### File Permissions (Production)
```bash
# Set secure permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 600 config/database.php
```

### Environment Variables (Optional)
```bash
# Set in .env file or server environment
DB_HOST=localhost
DB_NAME=astrogenix
DB_USER=username
DB_PASS=password
```

## 📊 Post-Installation Setup

### 1. Admin Panel Access
- URL: `https://yourdomain.com/astrogenix/admin/`
- Login with your admin credentials
- Configure site settings

### 2. Site Configuration
- Update site name and description
- Configure payment methods
- Set up email settings
- Customize mining contracts

### 3. Security Settings
- Enable SSL redirect
- Configure CSRF protection
- Set up rate limiting
- Review user permissions

### 4. Content Management
- Add blog posts
- Update about page
- Configure contact information
- Set up social media links

## 🛠️ Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check MySQL service
systemctl status mysql

# Verify credentials
mysql -u username -p

# Check PHP PDO extension
php -m | grep pdo
```

#### Permission Denied
```bash
# Fix ownership
chown -R www-data:www-data /var/www/html/astrogenix/

# Set permissions
chmod -R 755 /var/www/html/astrogenix/
```

#### White Screen / 500 Error
```bash
# Check PHP error log
tail -f /var/log/php/error.log

# Enable error display (development only)
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

### Log Files
- **Application Logs**: `logs/error.log`
- **PHP Logs**: `/var/log/php/error.log`
- **Web Server Logs**: `/var/log/apache2/error.log` or `/var/log/nginx/error.log`

## 📞 Support

If you encounter any issues during installation:

1. Check the troubleshooting section above
2. Review log files for error messages
3. Ensure all requirements are met
4. Contact support with detailed error information

## 🔄 Updates

To update AstroGenix:

1. Backup your database and files
2. Upload new files (preserve config/)
3. Run any database migrations
4. Clear cache if applicable

---

**Note**: Always test the installation on a staging environment before deploying to production.
