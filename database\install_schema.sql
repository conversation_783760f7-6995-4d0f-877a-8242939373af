-- AstroGenix Installation Schema
-- Complete database schema for fresh installation

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    total_invested DECIMAL(15,2) DEFAULT 0.00,
    total_earned DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    referred_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Investments (Mining Contracts) table
CREATE TABLE IF NOT EXISTS investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    min_amount DECIMAL(15,2) NOT NULL,
    max_amount DECIMAL(15,2) NOT NULL,
    daily_return DECIMAL(5,2) NOT NULL,
    duration_days INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User investments table
CREATE TABLE IF NOT EXISTS user_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    investment_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_return DECIMAL(15,2) NOT NULL,
    total_return DECIMAL(15,2) DEFAULT 0.00,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Withdrawals table
CREATE TABLE IF NOT EXISTS withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    payment_method VARCHAR(100),
    payment_details TEXT,
    admin_notes TEXT,
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL
);

-- Referral codes table
CREATE TABLE IF NOT EXISTS referral_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Referral earnings table
CREATE TABLE IF NOT EXISTS referral_earnings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    type ENUM('signup_bonus', 'mining_commission') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tasks table
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('daily', 'social', 'mining', 'referral', 'achievement') NOT NULL,
    reward_amount DECIMAL(15,2) NOT NULL,
    max_completions INT DEFAULT 1,
    sort_order INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User tasks table
CREATE TABLE IF NOT EXISTS user_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    reward_claimed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_task (user_id, task_id)
);

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Blog posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image VARCHAR(255),
    author_id INT NOT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Admin logs table
CREATE TABLE IF NOT EXISTS admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(255) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default site settings
INSERT IGNORE INTO site_settings (setting_key, setting_value) VALUES
('site_name', 'AstroGenix'),
('site_description', 'Mine cryptocurrency sustainably with AstroGenix green mining platform'),
('site_keywords', 'green crypto mining, sustainable cryptocurrency, eco-friendly mining, renewable energy crypto'),
('referral_signup_bonus', '5.00'),
('referral_investment_commission', '2.00'),
('min_withdrawal', '10.00'),
('withdrawal_fee', '1.00'),
('maintenance_mode', '0'),
('registration_enabled', '1'),
('email_verification_required', '0');

-- Insert sample mining contracts
INSERT IGNORE INTO investments (title, description, min_amount, max_amount, daily_return, duration_days, is_active) VALUES
('Green Solar Mining', 'Sustainable cryptocurrency mining powered by solar energy. Eco-friendly and profitable with guaranteed daily returns.', 100.00, 10000.00, 2.5, 30, 1),
('Wind Power Mining', 'Harness wind energy for cryptocurrency mining operations. Clean energy technology with consistent returns.', 250.00, 25000.00, 3.0, 45, 1),
('Hydro Mining Pro', 'Advanced hydroelectric-powered mining farms. Maximum efficiency with minimal environmental impact.', 500.00, 50000.00, 3.5, 60, 1),
('Eco Mining Elite', 'Premium green mining package combining solar, wind, and hydro power for optimal returns.', 1000.00, 100000.00, 4.0, 90, 1);

-- Insert sample tasks
INSERT IGNORE INTO tasks (title, description, category, reward_amount, sort_order, is_active) VALUES
-- Daily tasks
('Daily Login', 'Log in to your AstroGenix account every day to earn rewards', 'daily', 1.00, 1, 1),
('Check Mining Status', 'Review your active mining contracts and earnings daily', 'daily', 0.50, 2, 1),
('Visit Dashboard', 'Spend at least 2 minutes exploring your dashboard', 'daily', 0.75, 3, 1),

-- Social tasks
('Follow on Twitter', 'Follow @AstroGenix on Twitter for latest updates and news', 'social', 5.00, 1, 1),
('Join Telegram', 'Join our official Telegram community for support and discussions', 'social', 5.00, 2, 1),
('Share on Facebook', 'Share AstroGenix on your Facebook profile to spread the word', 'social', 3.00, 3, 1),
('LinkedIn Connection', 'Connect with AstroGenix on LinkedIn for professional updates', 'social', 4.00, 4, 1),

-- Mining tasks
('Complete First Investment', 'Make your first mining contract investment to get started', 'mining', 15.00, 1, 1),
('Invest $100+', 'Make a mining investment of $100 or more', 'mining', 10.00, 2, 1),
('Active for 7 Days', 'Keep mining contracts active for 7 consecutive days', 'mining', 20.00, 3, 1),
('Diversify Portfolio', 'Invest in at least 2 different mining contracts', 'mining', 25.00, 4, 1),

-- Referral tasks
('Invite First Friend', 'Invite your first friend to join AstroGenix using your referral code', 'referral', 10.00, 1, 1),
('5 Successful Referrals', 'Successfully refer 5 friends who make investments', 'referral', 50.00, 2, 1),
('Top Referrer', 'Become a top 10 referrer this month', 'referral', 100.00, 3, 1),
('Referral Master', 'Achieve 25+ successful referrals', 'referral', 200.00, 4, 1),

-- Achievement tasks
('Green Warrior', 'Complete 10 eco-friendly mining tasks', 'achievement', 25.00, 1, 1),
('Mining Expert', 'Reach $1000 total mining investments', 'achievement', 75.00, 2, 1),
('Community Leader', 'Help 3 new users get started with mining', 'achievement', 40.00, 3, 1),
('Sustainability Champion', 'Maintain active green mining for 30 days', 'achievement', 100.00, 4, 1);
