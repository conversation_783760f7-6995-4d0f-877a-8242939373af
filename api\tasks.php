<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

// Set JSON header
header('Content-Type: application/json');

// Require login
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

$action = $_POST['action'] ?? '';
$task_id = intval($_POST['task_id'] ?? 0);

if ($task_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid task ID']);
    exit;
}

try {
    switch ($action) {
        case 'complete_task':
            $result = completeTask($user_id, $task_id);
            echo json_encode($result);
            break;
            
        case 'claim_reward':
            $result = claimTaskReward($user_id, $task_id);
            echo json_encode($result);
            break;
            
        case 'get_task_stats':
            $stats = getTaskStats($user_id);
            echo json_encode(['success' => true, 'data' => $stats]);
            break;
            
        case 'get_user_tasks':
            $category = $_POST['category'] ?? null;
            $tasks = getUserTasks($user_id, $category);
            echo json_encode(['success' => true, 'data' => $tasks]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} catch (Exception $e) {
    logError("Task API error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request']);
}
?>
