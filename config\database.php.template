class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                )
            );
        } catch(PDOException $exception) {
            error_log("Database connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed. Please check your configuration.");
        }
        
        return $this->conn;
    }
}

// Global database connection
function getDB() {
    static $db = null;
    if ($db === null) {
        $database = new Database();
        $db = $database->getConnection();
    }
    return $db;
}

// Database utility functions
function executeQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query execution failed: " . $e->getMessage());
        throw new Exception("Database query failed.");
    }
}

function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

function insertRecord($table, $data) {
    $columns = array_keys($data);
    $placeholders = array_fill(0, count($columns), '?');
    
    $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
    
    try {
        $stmt = executeQuery($sql, array_values($data));
        return getDB()->lastInsertId();
    } catch (Exception $e) {
        error_log("Insert failed: " . $e->getMessage());
        throw new Exception("Failed to insert record.");
    }
}

function updateRecord($table, $data, $where, $whereParams = []) {
    $setParts = [];
    foreach (array_keys($data) as $column) {
        $setParts[] = "{$column} = ?";
    }
    
    $sql = "UPDATE {$table} SET " . implode(', ', $setParts) . " WHERE {$where}";
    $params = array_merge(array_values($data), $whereParams);
    
    try {
        $stmt = executeQuery($sql, $params);
        return $stmt->rowCount();
    } catch (Exception $e) {
        error_log("Update failed: " . $e->getMessage());
        throw new Exception("Failed to update record.");
    }
}

function deleteRecord($table, $where, $whereParams = []) {
    $sql = "DELETE FROM {$table} WHERE {$where}";
    
    try {
        $stmt = executeQuery($sql, $whereParams);
        return $stmt->rowCount();
    } catch (Exception $e) {
        error_log("Delete failed: " . $e->getMessage());
        throw new Exception("Failed to delete record.");
    }
}

// Transaction helpers
function beginTransaction() {
    return getDB()->beginTransaction();
}

function commit() {
    return getDB()->commit();
}

function rollback() {
    return getDB()->rollBack();
}
?>
