    </main>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white relative overflow-hidden">
        <!-- Background decoration -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
            <div class="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-leaf text-white text-2xl"></i>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-white"><?php echo getSiteSetting('site_name', 'AstroGenix'); ?></div>
                            <div class="text-sm text-gray-300 -mt-1">Green Mining</div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-8 max-w-md text-lg leading-relaxed">
                        <?php echo getSiteSetting('site_description', 'The world\'s most trusted platform for luxury investment opportunities. Join thousands of smart investors building wealth through premium assets.'); ?>
                    </p>

                    <!-- Social Media -->
                    <div class="flex space-x-4 mb-8">
                        <?php if (getSiteSetting('facebook_url')): ?>
                        <a href="<?php echo getSiteSetting('facebook_url'); ?>" target="_blank" class="group w-12 h-12 bg-white bg-opacity-15 rounded-xl flex items-center justify-center hover:bg-opacity-25 hover:bg-blue-500 transition-all duration-300 transform hover:scale-110 shadow-lg">
                            <i class="fab fa-facebook-f text-xl text-white group-hover:scale-110 transition-transform"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (getSiteSetting('twitter_url')): ?>
                        <a href="<?php echo getSiteSetting('twitter_url'); ?>" target="_blank" class="group w-12 h-12 bg-white bg-opacity-15 rounded-xl flex items-center justify-center hover:bg-opacity-25 hover:bg-blue-400 transition-all duration-300 transform hover:scale-110 shadow-lg">
                            <i class="fab fa-twitter text-xl text-white group-hover:scale-110 transition-transform"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (getSiteSetting('linkedin_url')): ?>
                        <a href="<?php echo getSiteSetting('linkedin_url'); ?>" target="_blank" class="group w-12 h-12 bg-white bg-opacity-15 rounded-xl flex items-center justify-center hover:bg-opacity-25 hover:bg-blue-600 transition-all duration-300 transform hover:scale-110 shadow-lg">
                            <i class="fab fa-linkedin-in text-xl text-white group-hover:scale-110 transition-transform"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (getSiteSetting('instagram_url')): ?>
                        <a href="<?php echo getSiteSetting('instagram_url'); ?>" target="_blank" class="group w-12 h-12 bg-white bg-opacity-15 rounded-xl flex items-center justify-center hover:bg-opacity-25 hover:bg-pink-500 transition-all duration-300 transform hover:scale-110 shadow-lg">
                            <i class="fab fa-instagram text-xl text-white group-hover:scale-110 transition-transform"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (getSiteSetting('telegram_url')): ?>
                        <a href="<?php echo getSiteSetting('telegram_url'); ?>" target="_blank" class="group w-12 h-12 bg-white bg-opacity-15 rounded-xl flex items-center justify-center hover:bg-opacity-25 hover:bg-blue-500 transition-all duration-300 transform hover:scale-110 shadow-lg">
                            <i class="fab fa-telegram text-xl text-white group-hover:scale-110 transition-transform"></i>
                        </a>
                        <?php endif; ?>
                    </div>

                    <!-- Trust badges -->
                    <div class="flex items-center space-x-4 text-sm text-gray-300">
                        <div class="flex items-center bg-white bg-opacity-10 px-3 py-2 rounded-lg">
                            <i class="fas fa-shield-alt mr-2 text-green-400"></i>
                            <span class="text-white font-medium">SEC Registered</span>
                        </div>
                        <div class="flex items-center bg-white bg-opacity-10 px-3 py-2 rounded-lg">
                            <i class="fas fa-lock mr-2 text-blue-400"></i>
                            <span class="text-white font-medium">Bank-Level Security</span>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-6 text-white">Platform</h3>
                    <ul class="space-y-3">
                        <li><a href="<?php echo getRelativePath('index.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                            <i class="fas fa-home mr-3 w-4 text-blue-400"></i>
                            <span>Home</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity text-blue-400"></i>
                        </a></li>
                        <li><a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                            <i class="fas fa-chart-line mr-3 w-4 text-green-400"></i>
                            <span>Investments</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity text-green-400"></i>
                        </a></li>
                        <li><a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                            <i class="fas fa-newspaper mr-3 w-4 text-purple-400"></i>
                            <span>Market Insights</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity text-purple-400"></i>
                        </a></li>
                        <li><a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                            <i class="fas fa-envelope mr-3 w-4 text-orange-400"></i>
                            <span>Contact</span>
                            <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity text-orange-400"></i>
                        </a></li>
                        <?php if (isLoggedIn()): ?>
                            <li><a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                                <i class="fas fa-tachometer-alt mr-3 w-4 text-cyan-400"></i>
                                <span>Dashboard</span>
                                <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity text-cyan-400"></i>
                            </a></li>
                        <?php else: ?>
                            <li><a href="<?php echo getRelativePath('pages/login.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                                <i class="fas fa-sign-in-alt mr-3 w-4 text-cyan-400"></i>
                                <span>Login</span>
                                <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity text-cyan-400"></i>
                            </a></li>
                            <li><a href="<?php echo getRelativePath('pages/register.php'); ?>" class="text-gray-300 hover:text-white transition-colors flex items-center group py-2 px-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                                <i class="fas fa-user-plus mr-3 w-4 text-green-400"></i>
                                <span>Get Started</span>
                                <i class="fas fa-arrow-right ml-auto opacity-0 group-hover:opacity-100 transition-opacity text-green-400"></i>
                            </a></li>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-xl font-bold mb-6 text-white">Get in Touch</h3>
                    <ul class="space-y-4">
                        <li class="flex items-center space-x-3 group p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-all">
                            <div class="w-10 h-10 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center group-hover:bg-opacity-30 transition-all">
                                <i class="fas fa-envelope text-blue-400"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-300">Email</div>
                                <div class="font-medium text-white"><?php echo getSiteSetting('contact_email', '<EMAIL>'); ?></div>
                            </div>
                        </li>
                        <?php if (getSiteSetting('contact_phone')): ?>
                        <li class="flex items-center space-x-3 group p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-all">
                            <div class="w-10 h-10 bg-purple-500 bg-opacity-20 rounded-lg flex items-center justify-center group-hover:bg-opacity-30 transition-all">
                                <i class="fas fa-phone text-purple-400"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-300">Phone</div>
                                <div class="font-medium text-white"><?php echo getSiteSetting('contact_phone'); ?></div>
                            </div>
                        </li>
                        <?php endif; ?>
                        <?php if (getSiteSetting('company_address')): ?>
                        <li class="flex items-center space-x-3 group p-3 rounded-lg hover:bg-white hover:bg-opacity-10 transition-all">
                            <div class="w-10 h-10 bg-orange-500 bg-opacity-20 rounded-lg flex items-center justify-center group-hover:bg-opacity-30 transition-all">
                                <i class="fas fa-map-marker-alt text-orange-400"></i>
                            </div>
                            <div>
                                <div class="text-sm text-gray-300">Location</div>
                                <div class="font-medium text-white"><?php echo getSiteSetting('company_address'); ?></div>
                            </div>
                        </li>
                        <?php endif; ?>
                        </li>
                        <li class="flex items-center space-x-3 group p-3 rounded-lg bg-green-500 bg-opacity-10 border border-green-500 border-opacity-20">
                            <div class="w-10 h-10 bg-green-500 bg-opacity-30 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-green-400"></i>
                            </div>
                            <div>
                                <div class="text-sm text-green-300">Support</div>
                                <div class="font-medium text-green-200">24/7 Available</div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Company Stats -->
            <div class="border-t border-white border-opacity-20 mt-12 pt-12">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                    <div class="group">
                        <div class="text-3xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors">$2.5B+</div>
                        <div class="text-sm text-gray-300">Assets Under Management</div>
                    </div>
                    <div class="group">
                        <div class="text-3xl font-bold text-white mb-2 group-hover:text-green-400 transition-colors">15,000+</div>
                        <div class="text-sm text-gray-300">Active Investors</div>
                    </div>
                    <div class="group">
                        <div class="text-3xl font-bold text-white mb-2 group-hover:text-purple-400 transition-colors">98.5%</div>
                        <div class="text-sm text-gray-300">Client Satisfaction</div>
                    </div>
                    <div class="group">
                        <div class="text-3xl font-bold text-white mb-2 group-hover:text-orange-400 transition-colors">24/7</div>
                        <div class="text-sm text-gray-300">Customer Support</div>
                    </div>
                </div>
            </div>

            <!-- Newsletter Signup -->
            <div class="border-t border-white border-opacity-20 mt-12 pt-12">
                <div class="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm rounded-3xl p-8 border border-white border-opacity-20 shadow-2xl">
                    <div class="flex flex-col lg:flex-row justify-between items-center">
                        <div class="mb-6 lg:mb-0 text-center lg:text-left">
                            <h3 class="text-2xl font-bold mb-3 text-white">Stay Ahead of the Market</h3>
                            <p class="text-gray-300 text-lg max-w-md">
                                Get exclusive investment opportunities, market insights, and expert analysis delivered to your inbox.
                            </p>
                            <div class="flex items-center justify-center lg:justify-start mt-4 text-sm text-gray-300">
                                <i class="fas fa-check-circle mr-2 text-green-400"></i>
                                <span>Weekly market reports • Exclusive deals • No spam</span>
                            </div>
                        </div>
                        <form class="flex flex-col sm:flex-row w-full lg:w-auto gap-3" onsubmit="subscribeNewsletter(event)">
                            <input
                                type="email"
                                placeholder="Enter your email address"
                                class="px-6 py-4 rounded-xl text-gray-900 flex-1 lg:w-80 focus:outline-none focus:ring-4 focus:ring-blue-300 shadow-lg"
                                required
                            >
                            <button
                                type="submit"
                                class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-bold whitespace-nowrap transition-all duration-300 transform hover:scale-105 shadow-lg"
                            >
                                <i class="fas fa-paper-plane mr-2"></i>
                                Subscribe Free
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-white border-opacity-20 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-left mb-4 md:mb-0">
                        <p class="text-gray-300">&copy; <?php echo date('Y'); ?> <?php echo getSiteSetting('site_name', 'AstroGenix'); ?> Green Mining. All rights reserved.</p>
                        <p class="text-sm text-gray-400 mt-1">Building sustainable wealth through green mining since 2024</p>
                    </div>
                    <div class="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
                        <a href="<?php echo getRelativePath('pages/privacy-policy.php'); ?>" class="text-gray-300 hover:text-white transition-colors hover:underline">Privacy Policy</a>
                        <a href="<?php echo getRelativePath('pages/terms-of-service.php'); ?>" class="text-gray-300 hover:text-white transition-colors hover:underline">Terms of Service</a>
                        <a href="<?php echo getRelativePath('pages/risk-disclosure.php'); ?>" class="text-gray-300 hover:text-white transition-colors hover:underline">Risk Disclosure</a>
                        <a href="<?php echo getRelativePath('pages/faq.php'); ?>" class="text-gray-300 hover:text-white transition-colors hover:underline">FAQ</a>
                    </div>
                </div>

                <!-- Additional compliance info -->
                <div class="mt-6 pt-6 border-t border-white border-opacity-10 text-center">
                    <p class="text-xs text-gray-400 max-w-4xl mx-auto leading-relaxed">
                        Cryptocurrency mining involves risk. Past performance is not indicative of future results.
                        AstroGenix is a registered green mining platform. All mining contracts are subject to market risk and may lose value.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script>
        // Newsletter subscription
        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;
            
            // Here you would typically send the email to your backend
            alert('Thank you for subscribing! We\'ll keep you updated with the latest investment opportunities.');
            event.target.reset();
        }
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Auto-hide flash messages
        setTimeout(() => {
            const alerts = document.querySelectorAll('[role="alert"]');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            });
        }, 5000);
        
        // Add loading states to forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                }
            });
        });
        
        // Format currency inputs
        document.querySelectorAll('input[type="number"][step="0.01"]').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value) {
                    this.value = parseFloat(this.value).toFixed(2);
                }
            });
        });
        
        // Copy to clipboard functionality
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show success message
                const toast = document.createElement('div');
                toast.className = 'fixed top-20 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
                toast.textContent = 'Copied to clipboard!';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            });
        }
        
        // Confirmation dialogs for dangerous actions
        document.querySelectorAll('[data-confirm]').forEach(element => {
            element.addEventListener('click', function(e) {
                const message = this.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
