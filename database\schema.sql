-- AstroGenix Green Mining Platform Database Schema
CREATE DATABASE IF NOT EXISTS astrogenix;
USE astrogenix;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    is_admin BOOLEAN DEFAULT FALSE,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'suspended') DEFAULT 'active',
    verification_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified',
    referred_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL,
    FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Mining contracts table (renamed from investments)
CREATE TABLE investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('bitcoin', 'ethereum', 'litecoin', 'solar_mining', 'wind_mining', 'hydro_mining', 'other') NOT NULL DEFAULT 'bitcoin',
    min_amount DECIMAL(15,2) NOT NULL DEFAULT 10.00,
    max_amount DECIMAL(15,2) NULL,
    monthly_rate DECIMAL(5,2) NOT NULL,
    duration_months INT NULL,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User investments table
CREATE TABLE user_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    investment_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    monthly_rate DECIMAL(5,2) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
);

-- Transactions table (deposits and withdrawals)
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    transaction_hash VARCHAR(255) NULL,
    wallet_address VARCHAR(255) NULL,
    screenshot_path VARCHAR(500) NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Daily profits table
CREATE TABLE daily_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_investment_id INT NOT NULL,
    profit_amount DECIMAL(15,2) NOT NULL,
    profit_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_profit (user_investment_id, profit_date)
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category VARCHAR(50) DEFAULT 'general',
    tags TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    image_url VARCHAR(500),
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User verifications table
CREATE TABLE user_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    passport_photo_path VARCHAR(500) NOT NULL,
    status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    admin_notes TEXT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    processed_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password_hash, is_admin, role, status, balance) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', TRUE, 'admin', 'active', 0.00);

-- Insert sample mining contracts
INSERT INTO investments (title, description, category, min_amount, max_amount, monthly_rate, duration_months, image_url) VALUES
('Solar Bitcoin Mining Farm', 'Eco-friendly Bitcoin mining operation powered by 100% solar energy. Located in Nevada desert with optimal sun exposure and cooling systems.', 'bitcoin', 100.00, 10000.00, 8.50, 24, 'https://images.unsplash.com/photo-1559302504-64aae6ca6b6d?w=400'),
('Wind-Powered Ethereum Mining', 'Sustainable Ethereum mining facility powered by wind turbines. Located in Texas with consistent wind patterns and green energy certification.', 'ethereum', 250.00, 25000.00, 12.00, 18, 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=400'),
('Hydro Litecoin Mining Pool', 'Hydroelectric-powered Litecoin mining operation in Iceland. Utilizing geothermal cooling and renewable energy sources.', 'litecoin', 50.00, 5000.00, 7.25, 36, 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400'),
('Green Bitcoin ASIC Farm', 'Advanced ASIC Bitcoin mining farm with carbon-neutral operations. Features latest generation miners and renewable energy integration.', 'bitcoin', 500.00, 50000.00, 10.50, 24, 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=400'),
('Eco Mining Diversified Pool', 'Diversified cryptocurrency mining portfolio across multiple green energy sources. Includes Bitcoin, Ethereum, and emerging altcoins.', 'other', 75.00, 7500.00, 6.75, 12, 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=400'),
('Solar Altcoin Mining Hub', 'Specialized altcoin mining facility focusing on energy-efficient cryptocurrencies. Powered by solar panels with battery storage.', 'solar_mining', 150.00, 15000.00, 15.00, 6, 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=400');

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Balance changes log table
CREATE TABLE IF NOT EXISTS balance_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    operation ENUM('add', 'subtract') NOT NULL,
    notes TEXT,
    old_balance DECIMAL(10,2) NOT NULL,
    new_balance DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Referral system tables
CREATE TABLE referral_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE referral_earnings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    type ENUM('signup_bonus', 'mining_commission') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Task system tables
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('daily', 'social', 'mining', 'referral', 'achievement') NOT NULL,
    reward_amount DECIMAL(15,2) NOT NULL,
    max_completions INT DEFAULT 1,
    sort_order INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE user_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task_id INT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    reward_claimed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_task (user_id, task_id)
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    image_url VARCHAR(500),
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, content, excerpt, image_url, is_published) VALUES
('Green Mining Revolution 2024', 'green-mining-revolution-2024', 'The green cryptocurrency mining revolution continues to gain momentum in 2024, with renewable energy-powered mining operations showing superior profitability and sustainability. Our analysis shows that solar and wind-powered mining farms are particularly attractive for consistent returns.', 'Analysis of current green mining trends and sustainable opportunities.', '/assets/images/blog-mining.jpg', TRUE),
('Sustainable Crypto Mining Growth', 'sustainable-crypto-mining-growth', 'The sustainable cryptocurrency mining industry has experienced unprecedented growth over the past year, with demand for eco-friendly mining contracts reaching new heights. This presents excellent opportunities for environmentally conscious investors.', 'Exploring the booming green mining market and investment potential.', '/assets/images/blog-green.jpg', TRUE);

-- Insert sample tasks
INSERT INTO tasks (title, description, category, reward_amount) VALUES
('Daily Check-in', 'Visit the platform daily to claim your bonus', 'daily', 1.00),
('First Mining Contract', 'Purchase your first mining contract', 'mining', 10.00),
('Refer a Friend', 'Invite a friend to join AstroGenix', 'referral', 25.00),
('Social Media Share', 'Share AstroGenix on social media', 'social', 5.00),
('Complete Profile', 'Complete your profile verification', 'daily', 15.00);
