<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

// Require login
requireLogin();

$page_title = 'Referral Program';
$user_id = $_SESSION['user_id'];

// Get referral stats
$referral_stats = getReferralStats($user_id);
$referral_code = getReferralCodeByUser($user_id);
$referral_earnings = getReferralEarnings($user_id);
$referral_details = getReferralDetails($user_id);
$monthly_earnings = getReferralEarningsByMonth($user_id, 6);

// Generate referral code if doesn't exist
if (!$referral_code) {
    $referral_code = generateReferralCode($user_id);
}

$referral_link = SITE_URL . '/pages/register.php?ref=' . $referral_code;

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-green-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">
                <i class="fas fa-users text-green-600 mr-3"></i>
                Referral Program
            </h1>
            <p class="text-xl text-gray-600">Earn rewards by inviting friends to AstroGenix</p>
        </div>

        <!-- Referral Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Total Referrals -->
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Referrals</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $referral_stats['total_referrals']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-user-friends text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Earnings -->
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Earnings</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo formatCurrency($referral_stats['total_earnings']); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- This Month -->
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">This Month</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $referral_stats['month_referrals']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-calendar text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Referral Link Section -->
            <div class="mining-card">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Your Referral Link</h2>
                    <p class="text-gray-600">Share this link with friends to earn $5 for each signup!</p>
                </div>

                <div class="space-y-4">
                    <!-- Referral Code -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Your Referral Code</label>
                        <div class="flex">
                            <input type="text" id="referral-code" value="<?php echo htmlspecialchars($referral_code); ?>" 
                                   class="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg bg-gray-50 text-lg font-mono" readonly>
                            <button onclick="copyToClipboard('referral-code')" 
                                    class="px-4 py-3 bg-green-600 text-white rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Referral Link -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Your Referral Link</label>
                        <div class="flex">
                            <input type="text" id="referral-link" value="<?php echo htmlspecialchars($referral_link); ?>" 
                                   class="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg bg-gray-50 text-sm" readonly>
                            <button onclick="copyToClipboard('referral-link')" 
                                    class="px-4 py-3 bg-green-600 text-white rounded-r-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Share Buttons -->
                    <div class="flex space-x-3">
                        <a href="https://wa.me/?text=Join%20AstroGenix%20green%20crypto%20mining%20platform%20and%20get%20$5%20bonus!%20<?php echo urlencode($referral_link); ?>" 
                           target="_blank" class="btn-green text-white flex-1 text-center">
                            <i class="fab fa-whatsapp mr-2"></i>WhatsApp
                        </a>
                        <a href="https://t.me/share/url?url=<?php echo urlencode($referral_link); ?>&text=Join%20AstroGenix%20green%20crypto%20mining%20platform%20and%20get%20$5%20bonus!" 
                           target="_blank" class="btn-green text-white flex-1 text-center">
                            <i class="fab fa-telegram mr-2"></i>Telegram
                        </a>
                    </div>
                </div>
            </div>

            <!-- How It Works -->
            <div class="mining-card">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">How It Works</h2>
                    <p class="text-gray-600">Earn money by referring friends to our platform</p>
                </div>

                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-green-600 font-bold">1</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900">Share Your Link</h3>
                            <p class="text-gray-600">Send your referral link to friends via social media, email, or messaging apps.</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-green-600 font-bold">2</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900">Friend Signs Up</h3>
                            <p class="text-gray-600">When someone registers using your link, they get $5 bonus and you earn $5 too!</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <span class="text-green-600 font-bold">3</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900">Earn Rewards</h3>
                            <p class="text-gray-600">Your earnings are automatically added to your account balance.</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                    <div class="flex items-center">
                        <i class="fas fa-gift text-green-600 mr-2"></i>
                        <span class="font-semibold text-green-800">Bonus: $5 for each successful referral!</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Referral Details -->
        <?php if (!empty($referral_details)): ?>
        <div class="mt-8">
            <div class="mining-card">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Your Referrals</h2>
                    <p class="text-gray-600">People who joined using your referral link</p>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Invested</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($referral_details as $referral): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                                            <span class="text-white font-bold"><?php echo strtoupper(substr($referral['username'], 0, 1)); ?></span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($referral['username']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('M j, Y', strtotime($referral['created_at'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                                    <?php echo formatCurrency($referral['total_invested']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($referral['total_invested'] > 0): ?>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active Investor</span>
                                    <?php else: ?>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Registered</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Monthly Earnings Chart -->
        <?php if (!empty($monthly_earnings)): ?>
        <div class="mt-8">
            <div class="mining-card">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Monthly Earnings</h2>
                    <p class="text-gray-600">Your referral earnings over the last 6 months</p>
                </div>

                <div class="space-y-4">
                    <?php foreach ($monthly_earnings as $month_data): ?>
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-semibold text-gray-900"><?php echo date('F Y', strtotime($month_data['month'] . '-01')); ?></div>
                            <div class="text-sm text-gray-600"><?php echo $month_data['referral_count']; ?> referrals</div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-green-600"><?php echo formatCurrency($month_data['total_earnings']); ?></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Referral Earnings History -->
        <?php if (!empty($referral_earnings)): ?>
        <div class="mt-8">
            <div class="mining-card">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Earnings History</h2>
                    <p class="text-gray-600">Track your referral earnings over time</p>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Referred User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($referral_earnings as $earning): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('M j, Y', strtotime($earning['created_at'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($earning['referred_username']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        <?php echo ucfirst(str_replace('_', ' ', $earning['type'])); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                                    <?php echo formatCurrency($earning['amount']); ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

    </div>
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show success message
    const button = element.nextElementSibling;
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.add('bg-green-700');
    
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.classList.remove('bg-green-700');
    }, 2000);
}
</script>

<?php include '../includes/footer.php'; ?>
