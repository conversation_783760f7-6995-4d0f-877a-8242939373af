<?php
/**
 * AstroGenix Installation Cleanup Script
 * Removes installation files for security
 */

// Check if already installed
if (!file_exists('config/installed.lock')) {
    die('Installation not completed. Please run install.php first.');
}

$files_to_remove = [
    'install.php',
    'cleanup_install.php', // This file itself
    'INSTALLATION.md',
    'config/database.php.template'
];

$directories_to_remove = [
    'install/'
];

$removed_files = [];
$removed_dirs = [];
$errors = [];

// Remove files
foreach ($files_to_remove as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            $removed_files[] = $file;
        } else {
            $errors[] = "Failed to remove file: {$file}";
        }
    }
}

// Remove directories
foreach ($directories_to_remove as $dir) {
    if (is_dir($dir)) {
        if (removeDirectory($dir)) {
            $removed_dirs[] = $dir;
        } else {
            $errors[] = "Failed to remove directory: {$dir}";
        }
    }
}

function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroGenix - Cleanup Complete</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 20px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            text-align: center;
        }
        .logo h1 {
            background: linear-gradient(135deg, #10b981, #34d399);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .success {
            color: #10b981;
            font-size: 4rem;
            margin: 1rem 0;
        }
        .message {
            color: #94a3b8;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        .details {
            background: rgba(15, 23, 42, 0.5);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            text-align: left;
        }
        .btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        }
        .error {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>AstroGenix</h1>
        </div>
        
        <?php if (empty($errors)): ?>
            <div class="success">🧹</div>
            <h2 style="color: #10b981; margin-bottom: 1rem;">Cleanup Complete!</h2>
            <p class="message">
                Installation files have been successfully removed for security.
            </p>
            
            <div class="details">
                <h3 style="color: #e2e8f0; margin-bottom: 1rem;">Removed Files:</h3>
                <?php if (!empty($removed_files)): ?>
                    <ul style="color: #6ee7b7; margin-left: 1rem;">
                        <?php foreach ($removed_files as $file): ?>
                            <li><?= htmlspecialchars($file) ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <?php if (!empty($removed_dirs)): ?>
                    <h4 style="color: #e2e8f0; margin: 1rem 0 0.5rem 0;">Removed Directories:</h4>
                    <ul style="color: #6ee7b7; margin-left: 1rem;">
                        <?php foreach ($removed_dirs as $dir): ?>
                            <li><?= htmlspecialchars($dir) ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            
            <a href="index.php" class="btn">Go to Your Site</a>
            
        <?php else: ?>
            <div class="success">⚠️</div>
            <h2 style="color: #f59e0b; margin-bottom: 1rem;">Cleanup Issues</h2>
            <p class="message">
                Some files could not be removed automatically.
            </p>
            
            <?php foreach ($errors as $error): ?>
                <div class="error"><?= htmlspecialchars($error) ?></div>
            <?php endforeach; ?>
            
            <div class="details">
                <h3 style="color: #e2e8f0; margin-bottom: 1rem;">Manual Cleanup Required:</h3>
                <p style="color: #94a3b8; margin-bottom: 1rem;">
                    Please manually delete the following files and directories:
                </p>
                <ul style="color: #fbbf24; margin-left: 1rem;">
                    <li>install.php</li>
                    <li>install/ directory</li>
                    <li>cleanup_install.php</li>
                    <li>INSTALLATION.md</li>
                    <li>config/database.php.template</li>
                </ul>
            </div>
            
            <a href="index.php" class="btn">Continue to Site</a>
        <?php endif; ?>
    </div>
    
    <script>
        // Auto-redirect after 10 seconds
        setTimeout(() => {
            window.location.href = 'index.php';
        }, 10000);
    </script>
</body>
</html>
