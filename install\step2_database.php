<h2 style="color: #10b981; margin-bottom: 1rem;">Database Configuration</h2>

<p style="margin-bottom: 2rem; color: #94a3b8;">
    Please provide your database connection details. Make sure the database user has permission to create databases and tables.
</p>

<form method="POST">
    <div class="form-group">
        <label for="db_host">Database Host</label>
        <input type="text" id="db_host" name="db_host" value="<?= $_POST['db_host'] ?? 'localhost' ?>" required>
        <small style="color: #64748b;">Usually 'localhost' for local servers</small>
    </div>
    
    <div class="form-group">
        <label for="db_name">Database Name</label>
        <input type="text" id="db_name" name="db_name" value="<?= $_POST['db_name'] ?? 'astrogenix' ?>" required>
        <small style="color: #64748b;">Will be created if it doesn't exist</small>
    </div>
    
    <div class="form-group">
        <label for="db_user">Database Username</label>
        <input type="text" id="db_user" name="db_user" value="<?= $_POST['db_user'] ?? 'root' ?>" required>
    </div>
    
    <div class="form-group">
        <label for="db_pass">Database Password</label>
        <input type="password" id="db_pass" name="db_pass" value="<?= $_POST['db_pass'] ?? '' ?>">
        <small style="color: #64748b;">Leave empty if no password is set</small>
    </div>
    
    <button type="submit" class="btn">Test Connection & Continue</button>
</form>

<div style="margin-top: 2rem; padding: 1rem; background: rgba(59, 130, 246, 0.1); border: 1px solid rgba(59, 130, 246, 0.3); border-radius: 8px;">
    <h4 style="color: #60a5fa; margin-bottom: 0.5rem;">Database Requirements:</h4>
    <ul style="color: #93c5fd; margin-left: 1rem;">
        <li>MySQL 5.7+ or MariaDB 10.2+</li>
        <li>User must have CREATE, DROP, INSERT, UPDATE, DELETE, SELECT privileges</li>
        <li>Recommended: User should have CREATE DATABASE privilege</li>
    </ul>
</div>
