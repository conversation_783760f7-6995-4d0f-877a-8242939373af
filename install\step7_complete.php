<?php
session_start();
$admin_info = $_SESSION['admin_info'] ?? null;
$db_config = $_SESSION['db_config'] ?? null;

// Clear session data
session_destroy();
?>

<div style="text-align: center;">
    <div style="font-size: 4rem; color: #10b981; margin-bottom: 1rem;">🎉</div>
    <h2 style="color: #10b981; margin-bottom: 1rem;">Installation Complete!</h2>
    <p style="color: #94a3b8; margin-bottom: 2rem; font-size: 1.1rem;">
        AstroGenix has been successfully installed and configured.
    </p>
</div>

<div style="background: rgba(15, 23, 42, 0.5); border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem;">
    <h3 style="color: #e2e8f0; margin-bottom: 1rem;">Installation Summary</h3>
    
    <div style="display: grid; gap: 1rem;">
        <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid rgba(64, 748, 107, 0.1);">
            <span style="color: #94a3b8;">Database:</span>
            <span style="color: #10b981;"><?= $db_config['name'] ?? 'astrogenix' ?></span>
        </div>
        
        <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid rgba(64, 748, 107, 0.1);">
            <span style="color: #94a3b8;">Admin Username:</span>
            <span style="color: #10b981;"><?= $admin_info['username'] ?? 'admin' ?></span>
        </div>
        
        <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid rgba(64, 748, 107, 0.1);">
            <span style="color: #94a3b8;">Admin Email:</span>
            <span style="color: #10b981;"><?= $admin_info['email'] ?? '<EMAIL>' ?></span>
        </div>
        
        <div style="display: flex; justify-content: space-between; padding: 0.5rem 0;">
            <span style="color: #94a3b8;">Installation Date:</span>
            <span style="color: #10b981;"><?= date('Y-m-d H:i:s') ?></span>
        </div>
    </div>
</div>

<div style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem;">
    <h3 style="color: #10b981; margin-bottom: 1rem;">Next Steps</h3>
    <ol style="color: #6ee7b7; margin-left: 1rem; line-height: 1.6;">
        <li>Delete the <code>install.php</code> file and <code>install/</code> directory for security</li>
        <li>Configure your web server (Apache/Nginx) if needed</li>
        <li>Set up SSL certificate for production use</li>
        <li>Review and customize site settings in the admin panel</li>
        <li>Add your mining contracts and configure payment methods</li>
    </ol>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 2rem;">
    <a href="../index.php" style="display: block; background: linear-gradient(135deg, #10b981, #059669); color: white; text-decoration: none; padding: 1rem; border-radius: 8px; text-align: center; font-weight: 600;">
        Visit Your Site
    </a>
    
    <a href="../admin/" style="display: block; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; text-decoration: none; padding: 1rem; border-radius: 8px; text-align: center; font-weight: 600;">
        Admin Panel
    </a>
</div>

<div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 8px; padding: 1rem;">
    <h4 style="color: #fca5a5; margin-bottom: 0.5rem;">⚠️ Security Notice</h4>
    <p style="color: #fca5a5; font-size: 0.9rem;">
        For security reasons, please delete the installation files immediately:
        <br><code>install.php</code> and the <code>install/</code> directory
    </p>
</div>

<script>
// Auto-redirect after 30 seconds
setTimeout(() => {
    if (confirm('Installation complete! Redirect to your site now?')) {
        window.location.href = '../index.php';
    }
}, 30000);
</script>
