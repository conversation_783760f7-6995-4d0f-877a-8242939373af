<?php
/**
 * AstroGenix Installation Script
 * Complete automated installation for AstroGenix Green Mining Platform
 * 
 * @version 1.0
 * <AUTHOR> Team
 */

// Prevent direct access if already installed
if (file_exists('config/installed.lock')) {
    die('AstroGenix is already installed. Delete config/installed.lock to reinstall.');
}

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Installation steps
$steps = [
    1 => 'System Requirements Check',
    2 => 'Database Configuration', 
    3 => 'Database Creation',
    4 => 'Admin Account Setup',
    5 => 'File Permissions',
    6 => 'Final Configuration',
    7 => 'Installation Complete'
];

$current_step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$errors = [];
$success = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($current_step) {
        case 2:
            $result = handleDatabaseConfig();
            if ($result['success']) {
                $current_step = 3;
            } else {
                $errors = $result['errors'];
            }
            break;
            
        case 4:
            $result = handleAdminSetup();
            if ($result['success']) {
                $current_step = 5;
            } else {
                $errors = $result['errors'];
            }
            break;
    }
}

// Auto-advance certain steps
if ($current_step === 1 && empty($errors)) {
    $requirements = checkSystemRequirements();
    if ($requirements['all_passed']) {
        $current_step = 2;
    } else {
        $errors = $requirements['errors'];
    }
}

if ($current_step === 3) {
    $db_result = createDatabase();
    if ($db_result['success']) {
        $current_step = 4;
        $success[] = 'Database created successfully';
    } else {
        $errors = $db_result['errors'];
        $current_step = 2;
    }
}

if ($current_step === 5) {
    $perm_result = checkFilePermissions();
    if ($perm_result['success']) {
        $current_step = 6;
        $success[] = 'File permissions configured';
    } else {
        $errors = $perm_result['errors'];
    }
}

if ($current_step === 6) {
    $config_result = finalizeConfiguration();
    if ($config_result['success']) {
        $current_step = 7;
        $success[] = 'Installation completed successfully';
    } else {
        $errors = $config_result['errors'];
    }
}

/**
 * Check system requirements
 */
function checkSystemRequirements() {
    $requirements = [
        'php_version' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'mbstring' => extension_loaded('mbstring'),
        'openssl' => extension_loaded('openssl'),
        'curl' => extension_loaded('curl'),
        'gd' => extension_loaded('gd'),
        'writable_config' => is_writable('config/'),
        'writable_assets' => is_writable('assets/'),
        'writable_logs' => is_writable('.') || mkdir('logs', 0755, true)
    ];
    
    $errors = [];
    foreach ($requirements as $req => $passed) {
        if (!$passed) {
            switch ($req) {
                case 'php_version':
                    $errors[] = 'PHP 7.4 or higher required. Current: ' . PHP_VERSION;
                    break;
                case 'pdo_mysql':
                    $errors[] = 'PDO MySQL extension required';
                    break;
                case 'mbstring':
                    $errors[] = 'Mbstring extension required';
                    break;
                case 'openssl':
                    $errors[] = 'OpenSSL extension required';
                    break;
                case 'curl':
                    $errors[] = 'cURL extension required';
                    break;
                case 'gd':
                    $errors[] = 'GD extension required';
                    break;
                case 'writable_config':
                    $errors[] = 'config/ directory must be writable';
                    break;
                case 'writable_assets':
                    $errors[] = 'assets/ directory must be writable';
                    break;
                case 'writable_logs':
                    $errors[] = 'Cannot create logs directory';
                    break;
            }
        }
    }
    
    return [
        'all_passed' => empty($errors),
        'requirements' => $requirements,
        'errors' => $errors
    ];
}

/**
 * Handle database configuration
 */
function handleDatabaseConfig() {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'astrogenix';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    
    $errors = [];
    
    // Validate inputs
    if (empty($db_host)) $errors[] = 'Database host is required';
    if (empty($db_name)) $errors[] = 'Database name is required';
    if (empty($db_user)) $errors[] = 'Database username is required';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    // Test database connection
    try {
        $dsn = "mysql:host={$db_host};charset=utf8mb4";
        $pdo = new PDO($dsn, $db_user, $db_pass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Save database config
        $config_content = "<?php\n";
        $config_content .= "// Database configuration\n";
        $config_content .= "define('DB_HOST', '{$db_host}');\n";
        $config_content .= "define('DB_NAME', '{$db_name}');\n";
        $config_content .= "define('DB_USER', '{$db_user}');\n";
        $config_content .= "define('DB_PASS', '{$db_pass}');\n\n";
        $config_content .= file_get_contents('config/database.php.template');
        
        file_put_contents('config/database.php', $config_content);
        
        // Save to session for next step
        session_start();
        $_SESSION['db_config'] = [
            'host' => $db_host,
            'name' => $db_name,
            'user' => $db_user,
            'pass' => $db_pass
        ];
        
        return ['success' => true];
        
    } catch (PDOException $e) {
        return ['success' => false, 'errors' => ['Database connection failed: ' . $e->getMessage()]];
    }
}

/**
 * Create database and tables
 */
function createDatabase() {
    session_start();
    $db_config = $_SESSION['db_config'] ?? null;
    
    if (!$db_config) {
        return ['success' => false, 'errors' => ['Database configuration not found']];
    }
    
    try {
        // Connect and create database
        $dsn = "mysql:host={$db_config['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $db_config['user'], $db_config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_config['name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$db_config['name']}`");
        
        // Execute schema
        $schema = file_get_contents('database/install_schema.sql');
        $statements = array_filter(array_map('trim', explode(';', $schema)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                $pdo->exec($statement);
            }
        }
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['Database creation failed: ' . $e->getMessage()]];
    }
}

/**
 * Handle admin account setup
 */
function handleAdminSetup() {
    $username = $_POST['admin_username'] ?? '';
    $email = $_POST['admin_email'] ?? '';
    $password = $_POST['admin_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    $errors = [];
    
    // Validate inputs
    if (empty($username)) $errors[] = 'Admin username is required';
    if (empty($email)) $errors[] = 'Admin email is required';
    if (empty($password)) $errors[] = 'Admin password is required';
    if ($password !== $confirm_password) $errors[] = 'Passwords do not match';
    if (strlen($password) < 6) $errors[] = 'Password must be at least 6 characters';
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Invalid email address';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    try {
        session_start();
        $db_config = $_SESSION['db_config'];
        
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $db_config['user'], $db_config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Create admin user
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash, is_admin, balance) VALUES (?, ?, ?, 1, 0)");
        $stmt->execute([$username, $email, $password_hash]);
        
        // Save admin info for final step
        $_SESSION['admin_info'] = [
            'username' => $username,
            'email' => $email,
            'password' => $password
        ];
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['Admin creation failed: ' . $e->getMessage()]];
    }
}

/**
 * Check and set file permissions
 */
function checkFilePermissions() {
    $directories = [
        'assets/uploads/',
        'logs/',
        'config/'
    ];
    
    $errors = [];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                $errors[] = "Cannot create directory: {$dir}";
                continue;
            }
        }
        
        if (!is_writable($dir)) {
            if (!chmod($dir, 0755)) {
                $errors[] = "Cannot set permissions for: {$dir}";
            }
        }
    }
    
    return [
        'success' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Finalize configuration
 */
function finalizeConfiguration() {
    try {
        // Create installation lock file
        file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
        
        // Update site settings
        session_start();
        $db_config = $_SESSION['db_config'];
        
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $db_config['user'], $db_config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Update admin email in site settings
        $admin_email = $_SESSION['admin_info']['email'];
        $stmt = $pdo->prepare("INSERT INTO site_settings (setting_key, setting_value) VALUES ('admin_email', ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$admin_email, $admin_email]);
        
        // Generate security keys
        $csrf_key = bin2hex(random_bytes(32));
        $stmt = $pdo->prepare("INSERT INTO site_settings (setting_key, setting_value) VALUES ('csrf_secret', ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$csrf_key, $csrf_key]);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['Configuration failed: ' . $e->getMessage()]];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AstroGenix Installation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 20px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo h1 {
            background: linear-gradient(135deg, #10b981, #34d399);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .logo p {
            color: #94a3b8;
            font-size: 1.1rem;
        }
        .progress {
            background: rgba(15, 23, 42, 0.5);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        .progress-bar {
            background: rgba(16, 185, 129, 0.2);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        .progress-fill {
            background: linear-gradient(90deg, #10b981, #34d399);
            height: 100%;
            transition: width 0.3s ease;
        }
        .steps {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #64748b;
        }
        .step.active { color: #10b981; font-weight: bold; }
        .step.completed { color: #34d399; }
        .form-group {
            margin-bottom: 1.5rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #e2e8f0;
            font-weight: 500;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            background: rgba(15, 23, 42, 0.5);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 8px;
            color: #e2e8f0;
            font-size: 1rem;
        }
        input:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        .btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        }
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
        }
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #6ee7b7;
        }
        .requirements {
            background: rgba(15, 23, 42, 0.5);
            border-radius: 8px;
            padding: 1rem;
        }
        .req-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(64, 748, 107, 0.1);
        }
        .req-item:last-child { border-bottom: none; }
        .req-status {
            font-weight: bold;
        }
        .req-pass { color: #10b981; }
        .req-fail { color: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>AstroGenix</h1>
            <p>Green Mining Platform Installation</p>
        </div>
        
        <div class="progress">
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?= ($current_step / 7) * 100 ?>%"></div>
            </div>
            <div class="steps">
                <?php foreach ($steps as $num => $title): ?>
                    <div class="step <?= $num < $current_step ? 'completed' : ($num === $current_step ? 'active' : '') ?>">
                        <?= $num ?>. <?= $title ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <strong>Errors:</strong>
                <ul style="margin-top: 0.5rem; margin-left: 1rem;">
                    <?php foreach ($errors as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <?php foreach ($success as $msg): ?>
                    <div><?= htmlspecialchars($msg) ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <?php
        // Display current step content
        switch ($current_step) {
            case 1:
                include 'install/step1_requirements.php';
                break;
            case 2:
                include 'install/step2_database.php';
                break;
            case 3:
                echo '<div class="alert alert-success">Creating database and tables...</div>';
                echo '<script>setTimeout(() => location.reload(), 2000);</script>';
                break;
            case 4:
                include 'install/step4_admin.php';
                break;
            case 5:
                echo '<div class="alert alert-success">Configuring file permissions...</div>';
                echo '<script>setTimeout(() => location.reload(), 2000);</script>';
                break;
            case 6:
                echo '<div class="alert alert-success">Finalizing installation...</div>';
                echo '<script>setTimeout(() => location.reload(), 2000);</script>';
                break;
            case 7:
                include 'install/step7_complete.php';
                break;
        }
        ?>
    </div>
</body>
</html>
