<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

// Require login
requireLogin();

$page_title = 'Mining Tasks';
$user_id = $_SESSION['user_id'];

// Handle task completion and reward claiming
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token';
    } else {
        $action = $_POST['action'] ?? '';
        $task_id = intval($_POST['task_id'] ?? 0);
        
        if ($action === 'complete_task' && $task_id > 0) {
            $result = completeTask($user_id, $task_id);
            if ($result['success']) {
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
        } elseif ($action === 'claim_reward' && $task_id > 0) {
            $result = claimTaskReward($user_id, $task_id);
            if ($result['success']) {
                $success_message = 'Reward claimed! +' . formatCurrency($result['amount']) . ' added to your balance.';
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

// Get user tasks and stats
$user_tasks = getUserTasks($user_id);
$task_stats = getTaskStats($user_id);

// Group tasks by category
$tasks_by_category = [];
foreach ($user_tasks as $task) {
    $tasks_by_category[$task['category']][] = $task;
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-green-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">
                <i class="fas fa-tasks text-green-600 mr-3"></i>
                Mining Tasks
            </h1>
            <p class="text-xl text-gray-600">Complete tasks to earn rewards and boost your mining operations</p>
        </div>

        <!-- Flash Messages -->
        <?php if ($success_message): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success_message); ?>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error_message); ?>
        </div>
        <?php endif; ?>

        <!-- Task Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Total Tasks -->
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Tasks</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $task_stats['total_tasks']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-list text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Completed Tasks -->
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Completed</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $task_stats['completed_tasks']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-check text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Completion Rate -->
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Completion Rate</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo $task_stats['completion_rate']; ?>%</p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-chart-pie text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Rewards -->
            <div class="mining-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Rewards</p>
                        <p class="text-3xl font-bold text-green-600"><?php echo formatCurrency($task_stats['total_rewards']); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-coins text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Categories -->
        <?php foreach ($tasks_by_category as $category => $tasks): ?>
        <div class="mb-8">
            <div class="mining-card">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        <?php 
                        $category_icons = [
                            'daily' => 'fas fa-calendar-day',
                            'social' => 'fas fa-share-alt',
                            'mining' => 'fas fa-microchip',
                            'referral' => 'fas fa-users',
                            'achievement' => 'fas fa-trophy'
                        ];
                        $icon = $category_icons[$category] ?? 'fas fa-tasks';
                        ?>
                        <i class="<?php echo $icon; ?> text-green-600 mr-3"></i>
                        <?php echo ucfirst($category); ?> Tasks
                    </h2>
                    <p class="text-gray-600">
                        <?php 
                        $category_descriptions = [
                            'daily' => 'Complete these tasks every day to earn consistent rewards',
                            'social' => 'Share and promote AstroGenix to earn social rewards',
                            'mining' => 'Mining-related tasks to optimize your operations',
                            'referral' => 'Invite friends and earn referral bonuses',
                            'achievement' => 'Special milestone achievements with big rewards'
                        ];
                        echo $category_descriptions[$category] ?? 'Complete these tasks to earn rewards';
                        ?>
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($tasks as $task): ?>
                    <div class="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-300">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($task['title']); ?></h3>
                                <p class="text-gray-600 text-sm mb-3"><?php echo htmlspecialchars($task['description']); ?></p>
                            </div>
                            <div class="ml-4">
                                <?php if ($task['is_completed']): ?>
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-white text-sm"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                        <i class="fas fa-clock text-gray-600 text-sm"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-coins text-yellow-500"></i>
                                <span class="font-semibold text-green-600"><?php echo formatCurrency($task['reward_amount']); ?></span>
                            </div>
                            
                            <div>
                                <?php if ($task['is_completed'] && !$task['reward_claimed']): ?>
                                    <button onclick="claimReward(<?php echo $task['id']; ?>)"
                                            class="btn-green text-white text-sm px-4 py-2 claim-btn"
                                            data-task-id="<?php echo $task['id']; ?>">
                                        <i class="fas fa-gift mr-1"></i>Claim Reward
                                    </button>
                                <?php elseif ($task['is_completed'] && $task['reward_claimed']): ?>
                                    <span class="px-3 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>Completed
                                    </span>
                                <?php else: ?>
                                    <button onclick="completeTask(<?php echo $task['id']; ?>)"
                                            class="btn-green text-white text-sm px-4 py-2 complete-btn"
                                            data-task-id="<?php echo $task['id']; ?>">
                                        <i class="fas fa-play mr-1"></i>Complete
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Progress bar if applicable -->
                        <?php if ($task['progress'] !== null && $task['progress'] < 100): ?>
                        <div class="mt-4">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>Progress</span>
                                <span><?php echo $task['progress']; ?>%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: <?php echo $task['progress']; ?>%"></div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>

        <?php if (empty($tasks_by_category)): ?>
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-tasks text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Tasks Available</h3>
            <p class="text-gray-600">Check back later for new mining tasks and rewards!</p>
        </div>
        <?php endif; ?>

    </div>
</div>

<script>
function completeTask(taskId) {
    const button = document.querySelector(`button[data-task-id="${taskId}"].complete-btn`);
    const originalHTML = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Processing...';
    button.disabled = true;

    fetch('../api/tasks.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=complete_task&task_id=${taskId}&csrf_token=<?php echo generateCSRFToken(); ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Reload page to show updated state
            setTimeout(() => location.reload(), 1000);
        } else {
            showMessage(data.message, 'error');
            button.innerHTML = originalHTML;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalHTML;
        button.disabled = false;
        showMessage('Failed to complete task. Please try again.', 'error');
    });
}

function claimReward(taskId) {
    const button = document.querySelector(`button[data-task-id="${taskId}"].claim-btn`);
    const originalHTML = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Claiming...';
    button.disabled = true;

    fetch('../api/tasks.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=claim_reward&task_id=${taskId}&csrf_token=<?php echo generateCSRFToken(); ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Reload page to show updated state
            setTimeout(() => location.reload(), 1000);
        } else {
            showMessage(data.message, 'error');
            button.innerHTML = originalHTML;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalHTML;
        button.disabled = false;
        showMessage('Failed to claim reward. Please try again.', 'error');
    });
}

function showMessage(message, type) {
    const alertClass = type === 'error' ? 'bg-red-50 border-red-200 text-red-700' : 'bg-green-50 border-green-200 text-green-700';
    const iconClass = type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';

    const messageDiv = document.createElement('div');
    messageDiv.className = `mb-6 ${alertClass} border px-4 py-3 rounded-lg animate-fade-in`;
    messageDiv.innerHTML = `<i class="${iconClass} mr-2"></i>${message}`;

    const container = document.querySelector('.max-w-7xl');
    const header = container.querySelector('h1').parentElement;
    header.insertAdjacentElement('afterend', messageDiv);

    // Remove message after 5 seconds
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}
</script>

<?php include '../includes/footer.php'; ?>
