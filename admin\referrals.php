<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

requireAdmin();

$page_title = 'Referral Management';

// Handle actions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_bonus_settings') {
            $signup_bonus = floatval($_POST['signup_bonus'] ?? 5.00);
            $investment_commission = floatval($_POST['investment_commission'] ?? 2.00);
            
            // Update settings in database or config
            // For now, we'll just show success message
            $success_message = 'Referral bonus settings updated successfully';
        }
    }
}

// Get referral statistics
$db = getDB();

// Top referrers
$stmt = $db->prepare("
    SELECT 
        u.id, u.username, u.email, u.created_at,
        COUNT(r.id) as total_referrals,
        COALESCE(SUM(re.amount), 0) as total_earnings
    FROM users u
    LEFT JOIN users r ON u.id = r.referred_by
    LEFT JOIN referral_earnings re ON u.id = re.referrer_id
    GROUP BY u.id
    HAVING total_referrals > 0
    ORDER BY total_referrals DESC, total_earnings DESC
    LIMIT 20
");
$stmt->execute();
$top_referrers = $stmt->fetchAll();

// Recent referral activities
$stmt = $db->prepare("
    SELECT 
        re.*, 
        u1.username as referrer_username,
        u2.username as referred_username
    FROM referral_earnings re
    JOIN users u1 ON re.referrer_id = u1.id
    JOIN users u2 ON re.referred_user_id = u2.id
    ORDER BY re.created_at DESC
    LIMIT 50
");
$stmt->execute();
$recent_activities = $stmt->fetchAll();

// Overall statistics
$stmt = $db->prepare("
    SELECT 
        COUNT(DISTINCT referrer_id) as total_referrers,
        COUNT(*) as total_referrals,
        SUM(amount) as total_paid_bonuses
    FROM referral_earnings
");
$stmt->execute();
$overall_stats = $stmt->fetch();

// Monthly statistics
$stmt = $db->prepare("
    SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as referrals_count,
        SUM(amount) as total_bonuses
    FROM referral_earnings
    WHERE created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month DESC
");
$stmt->execute();
$monthly_stats = $stmt->fetchAll();

include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Referral Management</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Referrals</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if ($success_message): ?>
    <div class="row">
        <div class="col-12">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="mdi mdi-check-circle-outline me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="row">
        <div class="col-12">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="mdi mdi-alert-circle-outline me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-account-multiple widget-icon bg-success-lighten text-success"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="Total Referrers">Total Referrers</h5>
                    <h3 class="mt-3 mb-3"><?php echo number_format($overall_stats['total_referrers'] ?? 0); ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-account-plus widget-icon bg-info-lighten text-info"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="Total Referrals">Total Referrals</h5>
                    <h3 class="mt-3 mb-3"><?php echo number_format($overall_stats['total_referrals'] ?? 0); ?></h3>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card widget-flat">
                <div class="card-body">
                    <div class="float-end">
                        <i class="mdi mdi-currency-usd widget-icon bg-warning-lighten text-warning"></i>
                    </div>
                    <h5 class="text-muted font-weight-normal mt-0" title="Total Bonuses Paid">Total Bonuses Paid</h5>
                    <h3 class="mt-3 mb-3"><?php echo formatCurrency($overall_stats['total_paid_bonuses'] ?? 0); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Referral Settings -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Referral Settings</h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_bonus_settings">
                        
                        <div class="mb-3">
                            <label for="signup_bonus" class="form-label">Signup Bonus ($)</label>
                            <input type="number" class="form-control" id="signup_bonus" name="signup_bonus" 
                                   value="5.00" step="0.01" min="0" max="100">
                            <small class="form-text text-muted">Bonus amount for successful referral signup</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="investment_commission" class="form-label">Investment Commission (%)</label>
                            <input type="number" class="form-control" id="investment_commission" name="investment_commission" 
                                   value="2.00" step="0.01" min="0" max="10">
                            <small class="form-text text-muted">Commission percentage on referred user investments</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Update Settings</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Monthly Statistics -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Monthly Statistics</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Month</th>
                                    <th>Referrals</th>
                                    <th>Bonuses</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($monthly_stats as $stat): ?>
                                <tr>
                                    <td><?php echo date('M Y', strtotime($stat['month'] . '-01')); ?></td>
                                    <td><?php echo number_format($stat['referrals_count']); ?></td>
                                    <td><?php echo formatCurrency($stat['total_bonuses']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Referrers -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Top Referrers</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Joined</th>
                                    <th>Total Referrals</th>
                                    <th>Total Earnings</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_referrers as $referrer): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($referrer['username']); ?></strong>
                                        <br><small class="text-muted">ID: <?php echo $referrer['id']; ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($referrer['email']); ?></td>
                                    <td><?php echo date('M j, Y', strtotime($referrer['created_at'])); ?></td>
                                    <td><span class="badge bg-primary"><?php echo $referrer['total_referrals']; ?></span></td>
                                    <td><strong class="text-success"><?php echo formatCurrency($referrer['total_earnings']); ?></strong></td>
                                    <td>
                                        <a href="users.php?id=<?php echo $referrer['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="mdi mdi-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">Recent Referral Activities</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Referrer</th>
                                    <th>Referred User</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_activities as $activity): ?>
                                <tr>
                                    <td><?php echo date('M j, Y H:i', strtotime($activity['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($activity['referrer_username']); ?></td>
                                    <td><?php echo htmlspecialchars($activity['referred_username']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $activity['type'] === 'signup_bonus' ? 'success' : 'info'; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $activity['type'])); ?>
                                        </span>
                                    </td>
                                    <td><strong class="text-success"><?php echo formatCurrency($activity['amount']); ?></strong></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/admin_footer.php'; ?>
