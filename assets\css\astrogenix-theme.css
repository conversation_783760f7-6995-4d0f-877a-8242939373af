/* AstroGenix Green Mining Theme */

/* Green Color Palette */
:root {
    --primary-green: #10b981;
    --secondary-green: #059669;
    --accent-green: #34d399;
    --dark-green: #047857;
    --light-green: #6ee7b7;
    --emerald: #10b981;
    --teal: #14b8a6;
    --forest: #065f46;
    --mint: #a7f3d0;
    --lime: #84cc16;
    
    /* Mining specific colors */
    --mining-gold: #fbbf24;
    --mining-silver: #e5e7eb;
    --mining-copper: #ea580c;
    --energy-blue: #3b82f6;
    --solar-yellow: #fde047;
    --wind-cyan: #06b6d4;
    --hydro-blue: #0ea5e9;
}

/* Green Gradient Backgrounds */
.bg-green-gradient {
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
}

.bg-eco-gradient {
    background: linear-gradient(135deg, var(--light-green), var(--primary-green), var(--dark-green));
}

.bg-mining-gradient {
    background: linear-gradient(135deg, var(--forest), var(--primary-green), var(--accent-green));
}

.bg-energy-gradient {
    background: linear-gradient(135deg, var(--solar-yellow), var(--primary-green), var(--energy-blue));
}

/* Mining Animations */
@keyframes mining-pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes hash-rate-flow {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes energy-flow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes mining-rotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes green-glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-green);
    }
    50% {
        box-shadow: 0 0 20px var(--accent-green), 0 0 30px var(--light-green);
    }
}

@keyframes leaf-sway {
    0%, 100% {
        transform: rotate(-5deg);
    }
    50% {
        transform: rotate(5deg);
    }
}

/* Mining Status Indicators */
.mining-active {
    animation: mining-pulse 2s ease-in-out infinite;
}

.hash-rate-indicator {
    position: relative;
    overflow: hidden;
}

.hash-rate-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, var(--accent-green), transparent);
    animation: hash-rate-flow 3s linear infinite;
}

.energy-source {
    background: linear-gradient(45deg, var(--primary-green), var(--accent-green), var(--primary-green));
    background-size: 200% 200%;
    animation: energy-flow 4s ease infinite;
}

.mining-rig {
    animation: mining-rotation 10s linear infinite;
}

.eco-badge {
    animation: green-glow 3s ease-in-out infinite;
}

.leaf-icon {
    animation: leaf-sway 3s ease-in-out infinite;
}

/* Green Button Styles */
.btn-green {
    background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-green:hover {
    background: linear-gradient(135deg, var(--secondary-green), var(--dark-green));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-green::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-green:hover::before {
    left: 100%;
}

/* Mining Dashboard Cards */
.mining-card {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mining-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-green), var(--accent-green));
}

.mining-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.15);
    border-color: var(--primary-green);
}

/* Progress Bars */
.progress-green {
    background-color: rgba(16, 185, 129, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.progress-green .progress-bar {
    background: linear-gradient(90deg, var(--primary-green), var(--accent-green));
    height: 8px;
    border-radius: 10px;
    transition: width 1s ease;
    position: relative;
}

.progress-green .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: hash-rate-flow 2s linear infinite;
}

/* Status Badges */
.status-mining {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    animation: mining-pulse 2s ease-in-out infinite;
}

.status-offline {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-maintenance {
    background: linear-gradient(135deg, var(--mining-gold), #f59e0b);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

/* Eco-friendly Icons */
.eco-icon {
    color: var(--primary-green);
    filter: drop-shadow(0 0 8px rgba(16, 185, 129, 0.3));
}

.solar-icon {
    color: var(--solar-yellow);
    animation: mining-rotation 20s linear infinite;
}

.wind-icon {
    color: var(--wind-cyan);
    animation: leaf-sway 2s ease-in-out infinite;
}

.hydro-icon {
    color: var(--hydro-blue);
    animation: energy-flow 3s ease infinite;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .mining-card {
        padding: 16px;
        margin-bottom: 16px;
    }

    .btn-green,
    .btn-secondary {
        padding: 12px 24px;
        font-size: 14px;
        min-height: 44px; /* Touch target size */
        width: 100%;
        margin-bottom: 8px;
    }

    /* Form optimizations for mobile */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    textarea,
    select {
        min-height: 44px;
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 12px;
        border-radius: 8px;
    }

    /* Table responsiveness */
    .table-responsive {
        font-size: 14px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Grid adjustments */
    .grid.grid-cols-2,
    .grid.grid-cols-3,
    .grid.grid-cols-4 {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    /* Text size adjustments */
    .text-4xl {
        font-size: 2rem;
        line-height: 2.5rem;
    }

    .text-3xl {
        font-size: 1.75rem;
        line-height: 2.25rem;
    }

    .text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }

    /* Spacing adjustments */
    .px-4,
    .px-6,
    .px-8 {
        padding-left: 16px;
        padding-right: 16px;
    }

    .py-8 {
        padding-top: 32px;
        padding-bottom: 32px;
    }

    /* Animation optimizations */
    .hash-rate-indicator::before {
        animation-duration: 2s;
    }

    .mining-active {
        animation-duration: 1.5s;
    }

    .mining-pulse {
        animation-duration: 3s;
    }

    .energy-flow {
        animation-duration: 4s;
    }

    /* Modal optimizations */
    .modal-content {
        margin: 16px;
        max-height: calc(100vh - 32px);
        overflow-y: auto;
    }

    /* Navigation improvements */
    .nav-mobile {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        z-index: 1000;
        padding: 16px;
        border-bottom: 1px solid rgba(16, 185, 129, 0.2);
    }

    /* Task and referral specific mobile optimizations */
    .task-card,
    .referral-card {
        margin-bottom: 16px;
        padding: 16px;
    }

    .task-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .referral-link-container {
        flex-direction: column;
        gap: 8px;
    }

    .referral-link-container input {
        border-radius: 8px;
        margin-bottom: 8px;
    }

    .referral-link-container button {
        border-radius: 8px;
        width: 100%;
    }

    .share-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .share-buttons a {
        width: 100%;
        text-align: center;
        padding: 12px;
    }

    /* Admin panel mobile optimizations */
    .admin-card {
        padding: 16px;
        margin-bottom: 16px;
    }

    .admin-table {
        font-size: 14px;
    }

    .admin-table th,
    .admin-table td {
        padding: 8px 4px;
    }

    .admin-actions {
        flex-direction: column;
        gap: 8px;
    }

    .admin-actions button,
    .admin-actions a {
        width: 100%;
        text-align: center;
    }
}

/* Small mobile devices (phones in portrait) */
@media (max-width: 480px) {
    .mining-card {
        padding: 12px;
    }

    .text-4xl {
        font-size: 1.75rem;
        line-height: 2rem;
    }

    .text-3xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .text-2xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }

    .px-4,
    .px-6,
    .px-8 {
        padding-left: 12px;
        padding-right: 12px;
    }

    .py-8 {
        padding-top: 24px;
        padding-bottom: 24px;
    }

    /* Smaller buttons on very small screens */
    .btn-green,
    .btn-secondary {
        padding: 10px 16px;
        font-size: 13px;
    }

    /* Compact stats cards */
    .stats-card .text-3xl {
        font-size: 1.25rem;
    }

    .stats-card .w-12.h-12 {
        width: 2.5rem;
        height: 2.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .mining-card {
        background: rgba(17, 24, 39, 0.95);
        border-color: rgba(16, 185, 129, 0.3);
        color: #f9fafb;
    }
    
    .mining-card:hover {
        background: rgba(17, 24, 39, 1);
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .mining-card:hover {
        transform: none;
        box-shadow: 0 10px 25px -5px rgba(16, 185, 129, 0.1), 0 10px 10px -5px rgba(16, 185, 129, 0.04);
    }

    .btn-green:hover,
    .btn-secondary:hover {
        transform: none;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    /* Add touch feedback */
    .btn-green:active,
    .btn-secondary:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    .mining-card:active {
        transform: scale(0.99);
        transition: transform 0.1s ease;
    }

    /* Larger touch targets */
    .btn-green,
    .btn-secondary,
    button,
    a[role="button"] {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 24px;
    }

    /* Touch-friendly form elements */
    input,
    textarea,
    select {
        min-height: 44px;
        padding: 12px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .mining-active,
    .hash-rate-indicator::before,
    .energy-source,
    .mining-rig,
    .eco-badge,
    .leaf-icon,
    .mining-pulse,
    .hash-rate-flow,
    .energy-flow,
    .mining-rotation,
    .green-glow,
    .leaf-sway {
        animation: none;
    }

    /* Provide alternative visual feedback */
    .mining-active {
        background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
        opacity: 0.9;
    }
}
